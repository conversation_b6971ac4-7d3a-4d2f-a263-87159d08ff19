[cerebrium.deployment]
name = "voice-bot"
python_version = "3.11"
include = ["./*", "./*.py", "./assets/**/*"]
exclude = ["./example_exclude"]
disable_auth = true
docker_base_image_url="debian:bullseye-slim"

[cerebrium.hardware]
region = "us-east-1"
provider = "aws"
compute = "CPU"
cpu = 2
memory = 4.0
gpu_count = 0

[cerebrium.runtime.custom]
port = 8765
entrypoint = ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8765"]

[cerebrium.scaling]
min_replicas = 3
max_replicas = 100
cooldown = 60
replica_concurrency = 1

[cerebrium.dependencies.pip]
requests = "latest"
openai = "latest"
twilio = "latest"
fastapi = "latest"
"pipecat-ai[daily, openai, twilio, azure, cartesia, silero]" = "0.0.60"
python-dotenv = "latest"
uvicorn = "latest"
loguru = "latest"
aiohttp = "latest"
pytz = "latest"
python-multipart = "latest"
babel = "latest"
librosa = "latest"
websockets = "latest"
supabase = "latest"
google-generativeai = "latest"
num2words = "latest"