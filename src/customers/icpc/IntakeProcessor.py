from pipecat.services.openai.llm import (
    OpenAILLMContext,
    OpenAILLMService,
)

from nodes.customer_base.intake_processor import CustomerIntakeProcessor
from nodes.appointment_form.intake_processor import AppointmentFormIntakeProcessor
from nodes.contact_form.intake_processor import ContactFormIntakeProcessor

from models import CompanyData, PatientData, PhoneCaller, Doctor, AppointmentForm
from constants import Intents
from lib.booking_provider import BookingProviderFactory

import utils.appointments
from loguru import logger

from nodes.appointment_form.node_functions import (
    get_main_motive,
    find_motive_by_id,
)

from .prompts import ICPCPrompts
from .tools import ICPCTools

from typing import List
from locales import get_locale

phrases = get_locale()


class IntakeProcessor(CustomerIntakeProcessor):
    def __init__(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
    ):

        super().__init__(
            llm,
            context,
            phone_caller=phone_caller,
            company_data=company_data,
            patient_data=patient_data,
        )

        self.doctolib_client = BookingProviderFactory.get_provider(company_data)
        self.prompts = ICPCPrompts(company_data)

        # Initialize appointment_form if not already created by parent
        if not hasattr(self, "appointment_form") or self.appointment_form is None:
            logger.info("Initializing appointment_form in ICPC IntakeProcessor")
            self.appointment_form = AppointmentForm(config=company_data.config)

        prompt, tools = self.prompts.init()
        context.set_tools(tools)
        context.add_message(prompt)

        if self._patient_data.medecin_historique:
            self.appointment_form.medecin = (
                self._patient_data.medecin_historique.name.split("-")[-1]
                .strip()
                .lower()
            )

    """
    Check Intent of the call
    """

    def __init_nodes__(self):
        self.contact_form_intake_processor = ContactFormIntakeProcessor(self)
        self.appointment_form_intake_processor = AppointmentFormIntakeProcessor(
            self, self.switcher_output_appointment_form_node
        )

        self.appointment_form_intake_processor.prompts.ask_doctor_name = (
            self.prompts.ask_doctor_name
        )

        functions = {}

        # Add functions from the current class
        for function_name in dir(self.__class__):
            if callable(
                getattr(self.__class__, function_name)
            ) and not function_name.startswith("_"):
                functions[function_name] = self

        # Add functions from contact form intake processor (only those that are not already implemented in the current class)
        for function_name in dir(self.appointment_form_intake_processor.__class__):
            if (
                callable(
                    getattr(
                        self.appointment_form_intake_processor.__class__, function_name
                    )
                )
                and not function_name.startswith("_")
                and function_name not in functions
            ):
                functions[function_name] = self.appointment_form_intake_processor

        # # Add functions from contact form intake processor (only those that are not already implemented in the current class)
        for function_name in dir(self.contact_form_intake_processor.__class__):
            if (
                callable(
                    getattr(self.contact_form_intake_processor.__class__, function_name)
                )
                and not function_name.startswith("_")
                and function_name not in functions
            ):
                functions[function_name] = self.contact_form_intake_processor

        # Return unique functions as a list of tuples
        return list(functions.items())

    async def handle_doctor_name(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        doctor_name_arg = args.get("doctor_name")

        # Handle None values properly - don't convert None to string "None"
        if doctor_name_arg is None or doctor_name_arg == "":
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Désolé, je n'ai pas compris le nom de l'établissement. Pouvez-vous répéter?",
                [ICPCTools.handle_doctor_name],
            )
            return

        arg_doctor_name = str(doctor_name_arg).split(" ")[0]

        if not arg_doctor_name:
            await self._reply_to_user(
                result_callback,
                context,
                llm,
                "Désolé, je n'ai pas compris le nom de l'établissement. Pouvez-vous répéter?",
                [ICPCTools.handle_doctor_name],
            )
            return

        if arg_doctor_name == "autre":
            prompt, tools = self.appointment_form_intake_processor.prompts.ask_motive()
            self.appointment_form.medecin = None
            self.appointment_form.practitioner_id = None
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        doctors = await self.booking_provider.get_doctors()
        if arg_doctor_name == "non":
            self.appointment_form.medecin = None
            prompt, tools = self.prompts.ask_doctor_name(doctors)
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        if (
            self.appointment_form.medecin != arg_doctor_name.lower()
            and "oui" not in arg_doctor_name.lower()
        ):
            self.appointment_form.medecin = arg_doctor_name.lower()
            prompt, tools = self.prompts.ask_doctor_name(
                doctors, Doctor(id=1, name=arg_doctor_name)
            )
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        await self._say_and_wait(
            llm,
            context,
            f"Parfait",
        )

        if (
            self.appointment_form.medecin
            and self.appointment_form.medecin.count("-") > 1
        ):
            self.appointment_form.medecin = (
                self.appointment_form.medecin.split("-")[-1].strip().lower()
            )

        all_agendas = self.company_data.inbound_config_file.calendars
        # only take the agendas with the name of the current doctor
        etablissments_calendars = [
            calendar.agenda_id
            for calendar in all_agendas
            if self.appointment_form.medecin.split(" ")[0] in calendar.name.lower()
        ]

        self.appointment_form_intake_processor.agenda_ids_to_check = (
            etablissments_calendars
        )

        print(self.appointment_form_intake_processor.agenda_ids_to_check)

        prompt, tools = self.appointment_form_intake_processor.prompts.ask_motive()
        await self._reply_to_user(result_callback, context, llm, prompt, tools)
        return

    async def switcher_output_appointment_form_node(
        self,
        function_name,
        tool_call_id,
        args,
        llm,
        context,
        result_callback,
    ):

        if (
            self.phone_caller.intent
            in [Intents.NOUVEAU, Intents.MODIFIER, Intents.CONFIRMER]
            and self.appointment_form.is_booking_created
            and self.company_data.bot_configuration.instructions_at_end
        ):
            await self._say_and_wait(
                llm, context, self.company_data.bot_configuration.instructions_at_end
            )

        await self.end_call(
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
            ask_last_question=True,
        )
