from models import Company<PERSON><PERSON>, Doctor, Speciality
from nodes.base.prompts import BasePrompts
from nodes.appointment_form.prompts import AppointmentFormPrompts
from nodes.contact_form.prompts import ContactFormPrompts
from typing import Dict, List, Any, Optional
from typing import TYPE_CHECKING
from collections import defaultdict
from .tools import ICPCTools

if TYPE_CHECKING:
    from nodes.appointment_form.intake_processor import AppointmentFormIntakeProcessor


class ICPCPrompts(BasePrompts):
    def __init__(self, company_data: CompanyData):
        self.company_data = company_data
        self.appointment_form_prompt = AppointmentFormPrompts(self.company_data)
        self.contact_form_prompt = ContactFormPrompts(self.company_data)

    def ask_doctor_name(
        self: "AppointmentFormIntakeProcessor",
        doctors: List[Doctor],
        main_doctor: Doctor = None,
        specific_speciality: Optional[Speciality] = None,
        return_node: Optional[dict[str, Any]] = None,
    ):

        doctors_str = [doctor.name.split("-")[-1] for doctor in doctors]
        doctors_str = list(set(doctors_str))
        doctors_str = ", ".join(doctors_str)

        sentence = (
            f"Souhaitez-vous prendre rendez-vous au ICPC {main_doctor.name.split('-')[-1]} ?"
            if main_doctor
            else "Dans quel établissement souhaitez-vous prendre rendez-vous ?"
        )
        print(sentence)

        message = self._set_message(
            f"""
            🗣️ Tu dois prononcer **exactement** cette phrase, sans aucun changement, ajout ou reformulation :
            👉 "{sentence}"
            ---
            Instructions pour traiter la réponse du patient:
            
            1. Utilise TOUJOURS la fonction 'handle_doctor_name' avec le nom exact de l'établissement mentionné par le patient.
            
            2. Correspondances spécifiques à gérer:
               - Si le patient dit 'oui' → utilise 'handle_doctor_name' avec la valeur 'oui'
               - Si le patient dit 'non' → utilise 'handle_doctor_name' avec la valeur 'non'
               - Si le patient dit 'pas de préférence', 'peu importe', 'autre', 'je ne sais pas' → utilise 'handle_doctor_name' avec la valeur 'autre'
            
            3. Pour tout autre nom d'établissement mentionné, utilise exactement le nom dit par le patient.
            
            4. Si le patient demande la liste des établissements disponibles, fournis-la en utilisant ce format:
               "Pour la spécialité [spécialité], les établissements disponibles sont : [établissement1], [établissement2], [établissement3]."
               
               Liste disponible:
               {doctors_str}
            
            5. Restrictions importantes:
               - Le patient doit uniquement répondre concernant le choix d'établissement
               - Ne confirme PAS le rendez-vous à cette étape (il reste des étapes à suivre)
               - N'accepte aucune autre question du patient
            
            """
        )
        print(doctors_str)

        tools = [
            ICPCTools.handle_doctor_name,
        ]

        tools.append(return_node) if return_node else None

        return message, tools
