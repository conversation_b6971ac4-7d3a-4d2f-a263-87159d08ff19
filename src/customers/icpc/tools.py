from nodes.base.tools import BaseTools
from nodes.appointment_form.tools import AppointmentFormTools
from nodes.contact_form.tools import ContactFormTools


create_tool_description = BaseTools.create_tool_description


class ICPCTools(BaseTools):
    def __init__(self):
        super().__init__()
        self.appointment_form_prompt = AppointmentFormTools()
        self.contact_form_prompt = ContactFormTools()

    handle_doctor_name = create_tool_description(
        name="handle_doctor_name",
        description="Utilise cette fonction pour traiter le choix d'établissement médical par le patient",
        properties=[
            {
                "name": "doctor_name",
                "type": "string",
                "description": "Le nom de l'établissement médical choisi par le patient, ou 'oui'/'non' pour confirmation, ou 'autre' si pas de préférence",
                "required": True,
            }
        ],
    )
