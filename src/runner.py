import asyncio
import sys
import argparse
import aiohttp
from loguru import logger
import importlib
from typing import Literal
from pipecat.transports.services.daily import (
    DailyTransport,
)
from pipecat.services.openai.llm import (
    OpenAILLMContext,
)

from pipecat.pipeline.pipeline import Pipeline
from pipecat.pipeline.runner import <PERSON><PERSON><PERSON><PERSON>unner
from pipecat.pipeline.task import PipelineParams, PipelineTask

from lib.booking_provider import BookingProviderFactory
from lib.twilio_client import TwilioClient
from lib.supabase_client import supabase_client
from lib.n8n_client import n8n_client
from models import CompanyData, PhoneCaller, PatientData, UpdateInboundTestLogs
import constants
import traceback
import processors
import transports
from tools import register_tools
from fastapi import WebSocket
from pipecat.transports.network.fastapi_websocket import FastAPIWebsocketTransport
from pipecat.services.openai.base_llm import OpenAILLMContextFrame
from pipecat.utils.text.markdown_text_filter import MarkdownTextFilter
from pipecat.frames.frames import TranscriptionFrame
from pipecat.utils.time import time_now_iso8601
from customers.utils import is_client_exists
from constants import NODE_ENV, DAILY_FULL_STACK_URL, LOG_LEVEL, Intents
import requests

# Logger configuration will be done in main() function to include call ID


async def main(
    mode: Literal["daily", "twilio"],
    company_data: CompanyData,
    phone_caller: PhoneCaller,
    patient_data: PatientData,
    config,
    websocket: WebSocket = None,
    stream_sid: str = None,
    speed_voice_rate: str = "1.15",
):
    # Reconfigure loggers to include call ID
    call_id = phone_caller.id
    logger.remove()

    # 1. Global handler: exclude `_stream_chat_completions` with call ID
    logger.add(
        sys.stderr,
        level=LOG_LEVEL,
        filter=lambda record: not (
            record["name"] == "pipecat.services.openai.base_llm"
            and record["function"] == "_stream_chat_completions"
        ),
        format=f"<green>{{time}}</green> | <blue>[{call_id}]</blue> | <level>{{level}}</level> | <level>{{message}}</level> | <cyan>{{extra}}</cyan>",
    )

    # 2. Specific handler: include only INFO-level logs from `_stream_chat_completions` with call ID
    logger.add(
        sys.stderr,
        level="INFO",
        filter=lambda record: (
            record["name"] == "pipecat.services.openai.base_llm"
            and record["function"] == "_stream_chat_completions"
        ),
        format=f"<green>{{time}}</green> | <blue>[{call_id}]</blue> | <level>{{message}}</level> | <cyan>{{extra}}</cyan>",
    )

    room_url = config.url if config else None
    token = config.token if config else None
    sipUri = config.sip if config else None
    print(f"SIP URI: {sipUri}")
    is_production = len(sipUri) > 0 if sipUri else False
    phone_caller.sip = sipUri
    phone_caller.room_url = room_url
    print(f"Is production: {is_production}")

    """Dynamically import the module based on the company name"""
    module_name = company_data.config_name
    if not is_client_exists(module_name):
        logger.warning(
            f"Customer folder for {module_name} does not exist. "
            "Using default bot configuration."
        )
        module_name = "default"

    is_legacy_hugo = module_name in ["imef"]
    print(f"Module name: {module_name}")

    module = importlib.import_module(
        f"customers.{module_name}.IntakeProcessor", package=__name__
    )
    IntakeProcessor = getattr(module, "IntakeProcessor")

    """ End of dynamic import """

    twilio_client = TwilioClient(
        constants.TWILIO_ACCOUNT_SID, constants.TWILIO_AUTH_TOKEN
    )

    res = requests.post(
        f"{DAILY_FULL_STACK_URL}/status/increment_running_clients",
        json={"call_id": phone_caller.id},
    )
    number_of_running_clients = res.json().get("number_of_running_clients", 0)
    print(f"Number of running clients: {number_of_running_clients}")

    async with aiohttp.ClientSession() as _:
        #################################
        # PipeCat Pipeline
        #################################
        transport = processors.init_daily_transport(
            mode, company_data, room_url, token, websocket, stream_sid
        )

        llm_model = "openai"
        stt = processors.init_stt_service("azure")
        llm = processors.init_llm_service(llm_model)
        md_filter = MarkdownTextFilter()

        tts = processors.init_tts_service("azure", md_filter, speed_voice_rate)

        messages = []
        context = OpenAILLMContext(messages=messages)
        context_aggregator = llm.create_context_aggregator(context)
        intake = IntakeProcessor(llm, context, company_data, phone_caller, patient_data)
        if is_legacy_hugo:
            intake.set_tts(tts)

        register_tools(intake, llm)

        stt_mute_processor = processors.init_mute_filter_service()
        user_idle = processors.init_user_idle(intake, context, stt, timeout=25)

        pipeline = Pipeline(
            [
                transport.input(),
                stt_mute_processor,
                stt,
                user_idle,
                context_aggregator.user(),
                llm,
                tts,
                transport.output(),
                context_aggregator.assistant(),
            ]
        )

        task = PipelineTask(
            pipeline,
            params=PipelineParams(enable_metrics=NODE_ENV != "production"),
            idle_timeout_secs=120,
            cancel_on_idle_timeout=True,
        )

        @task.event_handler("on_idle_timeout")
        async def on_idle_timeout(task):
            reason = "Idle timeout"
            logger.info(f"Participant idle timeout: {phone_caller.id}")
            await transports.on_idle_timeout(
                twilio_client, intake, reason, task, context
            )

        #################################
        # Event handlers
        #################################

        if mode == "twilio":

            @transport.event_handler("on_client_connected")
            async def _(transport, client):
                await task.queue_frames([OpenAILLMContextFrame(context)])

            @transport.event_handler("on_client_disconnected")
            async def _(transport: FastAPIWebsocketTransport, client: WebSocket):
                reason = "Client disconnected"
                try:
                    if is_legacy_hugo:
                        await transports.on_participant_left_legacy(
                            twilio_client, intake, reason, task
                        )
                        return
                    await transports.on_participant_left(
                        twilio_client, intake, reason, True, task, context
                    )
                except Exception as e:
                    logger.error(f"Error in on_client_disconnected: {str(e)}")
                    traceback.print_exc()

                await task.cancel()
                sys.exit(0)

        if mode == "daily":

            @transport.event_handler("on_dialin_ready")
            async def _(transport: DailyTransport, cdata):
                await transports.on_dialin_ready(
                    task, twilio_client, sipUri, phone_caller
                )

            @transport.event_handler("on_first_participant_joined")
            async def _(transport: DailyTransport, participant):
                phone_caller.intent = Intents.INIT_NODE
                await transports.on_first_participant_joined(
                    transport, participant, task, context, company_data
                )

            @transport.event_handler("on_participant_left")
            async def _(transport: DailyTransport, participant, reason):
                logger.info(
                    f"Participant left normally: {phone_caller.id}, reason: {reason}"
                )
                try:
                    if is_legacy_hugo:
                        await transports.on_participant_left_legacy(
                            twilio_client, intake, reason, task
                        )
                        return

                    await transports.on_participant_left(
                        twilio_client, intake, reason, is_production, task, context
                    )
                except Exception as e:
                    logger.error(f"Error in on_participant_left: {str(e)}")
                    traceback.print_exc()

                await task.cancel()

            @transport.event_handler("on_call_state_updated")
            async def _(transport: DailyTransport, state):
                await transports.on_call_state_updated(state, task)

            @transport.event_handler("on_dialin_error")
            async def on_dialin_error(transport, data):
                """Handler for dial-in errors."""
                logger.error(f"Dial-in error: {data}")
                # The bot should leave the call if there is an error
                try:
                    n8n_client.send_error_server(
                        config=company_data.config,
                        phone_caller=phone_caller,
                        traceback=str(data),
                    )
                except Exception as e:
                    logger.error(f"Failed to send error to n8n: {str(e)}")
                    n8n_client.send_error_server(
                        config=company_data.config,
                        phone_caller=phone_caller,
                        traceback=traceback.format_exc(),
                    )
                await task.cancel()

            @transport.event_handler("on_dialin_warning")
            async def on_dialin_warning(transport, data):
                """Handler for dial-in warnings."""
                logger.warning(f"Dial-in warning: {data}")
                try:
                    n8n_client.send_error_server(
                        config=company_data.config,
                        phone_caller=phone_caller,
                        traceback=str(data),
                    )
                except Exception as e:
                    logger.error(f"Failed to send warning to n8n: {str(e)}")
                    n8n_client.send_error_server(
                        config=company_data.config,
                        phone_caller=phone_caller,
                        traceback=traceback.format_exc(),
                    )

            @transport.event_handler("on_app_message")
            async def on_app_message(transport, data, sender: str):
                message = data.get("message", "")
                if not message:
                    logger.warning("Received empty message from app")
                    return
                await stt.push_frame(
                    TranscriptionFrame(
                        message,
                        "",
                        time_now_iso8601(),
                    )
                )

        runner = PipelineRunner(handle_sigint=False, force_gc=True)
        await runner.run(task)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Somed Flow")
    parser.add_argument("-u", "--url", type=str, help="Room URL")
    parser.add_argument("-t", "--token", type=str, help="Token")
    parser.add_argument("-i", "--call_id", type=str, help="Call ID")
    parser.add_argument("-s", "--sip", type=str, help="SIP URI")
    parser.add_argument("-c", "--config_id", type=str, help="Config ID", required=True)
    parser.add_argument(
        "-n", "--module_name", type=str, help="Module name", required=False
    )
    parser.add_argument("-p", "--phone_caller", type=str, help="Caller phone number")
    parser.add_argument(
        "--speed_voice", type=str, default="1.15", help="Speed voice rate"
    )
    parser.add_argument(
        "--test", action="store_true", help="Run in test mode", default=False
    )
    parsed_args = parser.parse_args()

    try:
        if supabase_client.db is None:
            raise ValueError("Supabase client is not initialized")

        company_data = supabase_client.get_company_data_by_config_id(
            parsed_args.config_id
        )

        if not company_data:
            raise ValueError("Company data not found")

        booking_provider = BookingProviderFactory.get_provider(company_data)
        phone_caller = PhoneCaller(
            id=parsed_args.call_id, phone_number=parsed_args.phone_caller
        )

        async def get_patients_data_with_retries(phone_number, retries=3, delay=2):
            for attempt in range(retries):
                try:
                    return await booking_provider.get_patient_information_by_center(
                        phone_number
                    )
                except Exception as e:
                    if attempt < retries - 1:
                        logger.warning(
                            f"Attempt {attempt + 1} failed to get patient data: {e}. Retrying in {delay} seconds..."
                        )
                        await asyncio.sleep(delay)
                    else:
                        logger.error(
                            f"All {retries} attempts failed to get patient data."
                        )
                        raise

        patients_data = asyncio.run(
            get_patients_data_with_retries(phone_caller.phone_number)
        )

        company_data.possible_patients = patients_data

        patient_data = (
            patients_data[0].model_copy()
            if len(patients_data) > 0
            else PatientData(is_new_patient=True)
        )

        print(f"Patient data found")

        speed_voice_rate = (
            parsed_args.speed_voice if parsed_args.speed_voice else "1.15"
        )

        test_mode = parsed_args.test
        if test_mode:
            company_data.is_test = True

        asyncio.run(
            main(
                "daily",
                company_data,
                phone_caller,
                patient_data,
                parsed_args,
                None,
                None,
                speed_voice_rate,
            )
        )
    except KeyboardInterrupt:
        logger.info("Pipeline interrupted by user")
    except Exception as e:
        logger.error(f"Error: {e}")
        logger.error(traceback.format_exc())
        n8n_client.send_error_server(
            config=parsed_args.config_id,
            phone_caller=phone_caller,
            traceback=traceback.format_exc(),
        )
