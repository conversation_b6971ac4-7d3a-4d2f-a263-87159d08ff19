from typing import Optional
from models import (
    <PERSON>ientData,
    <PERSON>tiveAppointment,
    CompanyData,
    PhoneCaller,
    AppointmentForm,
    Appointment,
)
import utils.appointments
from constants import AGE_CATEGORIES, Intents, MAX_APPOINTMENT_DAYS
from loguru import logger
from typing import List, Union
from rapidfuzz import fuzz
from lib.booking_provider import BookingProvider
import pytz
from datetime import datetime, timedelta, date, time, timedelta
from dateutil.parser import parse

from models.motive_appointments_with_injection_option import (
    MotiveAppointmentWithInjectionOption,
)
from nodes.appointment_form.utils.imaging import (
    strip_injection_option_from_motive_name,
    check_has_injection,
)

import utils.appointments


def calculate_age_float(birthdate: datetime, current_date=None):
    if current_date is None:
        current_date = datetime.date.today()
    days_in_year = 365.2425  # Average including leap years
    age = (current_date - birthdate).days / days_in_year
    return age


def get_children_motive_from_array(
    child_motives: List[int],
    patient_data: PatientData,
    company_data: CompanyData,
) -> Optional[int]:
    birthdate = patient_data.birthdate
    if not birthdate:
        logger.warning("No birthdate found for patient")
        return None

    current_date = datetime.now()
    patient_age = calculate_age_float(birthdate, current_date)

    for motive_id in child_motives:
        motive = find_motive_by_id(motive_id, company_data)
        if motive:
            if motive.age_minimum and motive.age_maximum:
                if (
                    patient_age >= motive.age_minimum
                    and patient_age <= motive.age_maximum
                ):

                    return motive.visit_motive_id

            if motive.age_minimum and not motive.age_maximum:
                if patient_age >= motive.age_minimum:
                    return motive.visit_motive_id

            if not motive.age_minimum and motive.age_maximum:
                if patient_age <= motive.age_maximum:
                    return motive.visit_motive_id

            if not motive.age_minimum and not motive.age_maximum:
                return motive.visit_motive_id

    logger.warning("No motive found for patient")

    def get_age_min(motive_id):
        motive = find_motive_by_id(motive_id, company_data)
        return (
            motive.age_minimum
            if motive and motive.age_minimum is not None
            else float("inf")
        )

    def get_age_max(motive_id):
        motive = find_motive_by_id(motive_id, company_data)
        return (
            motive.age_maximum
            if motive and motive.age_maximum is not None
            else float("-inf")
        )

    motive_with_smallest_age = min(
        child_motives,
        key=get_age_min,
    )
    motive_with_biggest_age = max(
        child_motives,
        key=get_age_max,
    )

    if (
        find_motive_by_id(motive_with_biggest_age, company_data)
        and find_motive_by_id(motive_with_biggest_age, company_data).age_maximum
        and patient_age
        > find_motive_by_id(motive_with_biggest_age, company_data).age_maximum
    ):
        logger.warning(
            f"Patient age {patient_age} is greater than maximum age {find_motive_by_id(motive_with_biggest_age, company_data).age_maximum}"
        )
        return None

    if (
        find_motive_by_id(motive_with_smallest_age, company_data)
        and find_motive_by_id(motive_with_smallest_age, company_data).age_minimum
        and patient_age
        < find_motive_by_id(motive_with_smallest_age, company_data).age_minimum
    ):
        logger.warning(
            f"Patient age {patient_age} is less than minimum age {find_motive_by_id(motive_with_smallest_age, company_data).age_minimum}"
        )
        return motive_with_smallest_age

    return None


def get_children_motive(
    child_motive: List[int] | int,
    patient_data: PatientData,
    company_data: CompanyData,
    old_motive_id: Optional[int] = None,
):
    if isinstance(child_motive, int):
        child_motive = [child_motive]

    if isinstance(child_motive, list):
        motive_id = get_children_motive_from_array(
            child_motive, patient_data, company_data
        )

        return motive_id if motive_id else old_motive_id

    return child_motive if child_motive else old_motive_id


def get_adult_motive(
    adult_to_enfants_consultations: dict,
    old_motive_id: int,
    company_data: CompanyData,
):
    if not adult_to_enfants_consultations:
        logger.warning("No adult to enfants consultations found")
        return old_motive_id

    if not isinstance(adult_to_enfants_consultations, dict):
        logger.warning("adult_to_enfants_consultations is not a dict")
        return old_motive_id

    for adult_motive_id, child_motives in adult_to_enfants_consultations.items():
        if isinstance(child_motives, list) and old_motive_id in child_motives:
            logger.info(
                f"Found adult motive {adult_motive_id} for old motive {old_motive_id}"
            )
            adult_motive_id = int(adult_motive_id)
            adult_motive = find_motive_by_id(adult_motive_id, company_data)
            return adult_motive

    logger.warning(
        f"No adult motive found for old motive {old_motive_id} in adult_to_enfants_consultations"
    )


MIN_SIMILARITY_THRESHOLD = 80


def find_motive_by_id(
    motive_id: int,
    company_data: CompanyData,
    doctor_name: Optional[str] = None,
):
    if not motive_id:
        logger.warning("No motive_id found")
        return None

    if isinstance(motive_id, str):
        motive_id = int(motive_id)
        logger.info(f"Converting motive_id to int: {motive_id}")

    motives = company_data.inbound_config_file.visit_motives_categories
    if doctor_name:
        motive = next(
            (
                motive
                for motive in motives
                if motive.visit_motive_id == motive_id
                and motive.medecin
                and fuzz.ratio(motive.medecin.lower(), doctor_name.lower())
                > MIN_SIMILARITY_THRESHOLD
            ),
            None,
        )

    for motive in motives:
        if int(motive.visit_motive_id) == int(motive_id):
            return motive

    return None


def find_motive_by_name(
    motive_name: str,
    company_data: CompanyData,
    speciality_id: Optional[int] = None,
    doctor_name: Optional[str] = None,
):

    motives = company_data.inbound_config_file.visit_motives_categories
    if doctor_name:
        motive = next(
            (
                motive
                for motive in motives
                if motive.visit_motive_name.lower() == motive_name.lower()
                and motive.medecin
                and fuzz.ratio(motive.medecin.lower(), doctor_name.lower())
                > MIN_SIMILARITY_THRESHOLD
            ),
            None,
        )
        if motive:
            return motive

    if speciality_id:
        motives = [
            motive for motive in motives if motive.speciality_id == speciality_id
        ]

    for motive in motives:
        if motive.visit_motive_name.lower() == motive_name.lower():
            return motive

    return None


def find_motives_with_same_name(
    motive: MotiveAppointment,
    company_data: CompanyData,
) -> List[MotiveAppointment]:
    print(f"Finding motive with same name: {motive.visit_motive_name}")
    motives = company_data.inbound_config_file.visit_motives_categories

    motives_found = []
    for item_motive in motives:
        if item_motive.visit_motive_id == motive.visit_motive_id:
            continue

        if (
            motive.visit_motive_name.replace("(tous)", "").strip().lower()
            == item_motive.visit_motive_name.strip().lower()
        ):
            print(f"Found motive with same name: {motive.visit_motive_name}")
            motives_found.append(item_motive)

    if not motives_found:
        logger.warning(f"No motives found with same name: {motive.visit_motive_name}")
        return []

    return motives_found


async def get_main_motive(
    patient_data: PatientData,
    main_motive: MotiveAppointment,
    current_to_premieres_consultations: dict,
    adult_to_enfants_consultations: dict,
    company_data: CompanyData,
    medecin: Optional[str] = None,
    arg_motive: Optional[str] = "",
):
    found_motive = main_motive
    old_motive = main_motive
    new_reason_motive_id = None
    if not main_motive:
        logger.info("No main motive found")
        return None

    if medecin and isinstance(medecin, int):
        logger.info("Medecin is an int, converting to str")
        medecin = str(medecin)

    if (patient_data.is_new_patient and "suivi" not in arg_motive.lower()) or (
        "première" in arg_motive.lower()
    ):
        logger.info("New patient, checking for new motive")
        print("old motive: " + str(old_motive.visit_motive_id))
        new_reason_motive_id = (
            old_motive
            and current_to_premieres_consultations
            and current_to_premieres_consultations.get(
                old_motive.visit_motive_id, main_motive.visit_motive_id
            )
        )
        print("new patient adult motive: " + str(new_reason_motive_id))

    if patient_data.age_category == AGE_CATEGORIES.ENFANT.value:
        logger.info("Patient is a child, checking for new motive")
        old_motive_id = (
            new_reason_motive_id if new_reason_motive_id else old_motive.visit_motive_id
        )
        new_reason_motive_id = get_children_motive(
            adult_to_enfants_consultations.get(
                old_motive_id, old_motive.visit_motive_id
            ),
            patient_data,
            company_data,
            old_motive_id,
        )

        if patient_data.is_new_patient and new_reason_motive_id:
            new_reason_motive_id = current_to_premieres_consultations.get(
                new_reason_motive_id, new_reason_motive_id
            )

    # Handle senior patients
    if patient_data.birthdate:
        age = calculate_age_float(patient_data.birthdate, datetime.now())
        if age >= 80:
            motive_id = (
                new_reason_motive_id
                if new_reason_motive_id
                else old_motive.visit_motive_id
            )
            if motive_id == 88586:
                new_reason_motive_id = 14152622

        if age >= 70:
            motive_id = (
                new_reason_motive_id
                if new_reason_motive_id
                else old_motive.visit_motive_id
            )

            if motive_id == 1731773:
                new_reason_motive_id = 1731777
            if motive_id == 2898841:
                new_reason_motive_id = 1731778

    if new_reason_motive_id:
        found_motive = find_motive_by_id(int(new_reason_motive_id), company_data)

        if found_motive and medecin:
            found_motive = find_motive_by_name(
                found_motive.visit_motive_name,
                company_data,
                found_motive.speciality_id,
                medecin,
            )

    if medecin and isinstance(medecin, str):
        found_motive = find_motive_by_name(
            main_motive.visit_motive_name,
            company_data,
            found_motive.speciality_id,
            medecin,
        )

    return found_motive if found_motive else main_motive


def get_agendas_ids_to_check(
    company_data: CompanyData,
    phone_caller: PhoneCaller,
    appointment_form: AppointmentForm,
    found_motive: MotiveAppointment,
    patient_data: PatientData,
    ask_doctor_name: bool,
    check_all_agendas_ids: bool = False,
) -> List[str]:
    # Default agenda IDs

    doctors = company_data.inbound_config_file.calendars
    if patient_data and patient_data.birthdate:
        patient_age = utils.appointments.calculate_age(patient_data.birthdate)
        print(f"Patient age: {patient_age}")
        doctors = [
            doctor
            for doctor in doctors
            if (doctor.age_minimum is None or patient_age >= doctor.age_minimum)
            and (doctor.age_maximum is None or patient_age <= doctor.age_maximum)
        ]

        removed_doctors = [
            doctor.name
            for doctor in company_data.inbound_config_file.calendars
            if doctor not in doctors
        ]
        print(f"Removed doctors: {removed_doctors}")

    if isinstance(found_motive, int):
        found_motive = find_motive_by_id(found_motive, company_data)

    agenda_ids_to_check = (
        [
            doctor.agenda_id
            for doctor in doctors
            if found_motive is not None
            and doctor.speciality_id == found_motive.speciality_id
        ]
        if not company_data.has_unique_agenda
        else company_data.agenda_id
    )

    # Check for new appointment with historical doctor matching the motive
    if (
        phone_caller.intent == Intents.NOUVEAU
        and not check_all_agendas_ids
        and patient_data.birthdate
        and not ask_doctor_name
        and found_motive
        and patient_data.historical_doctors
        and found_motive.speciality_id is not None
        and patient_data.historical_doctors != {}
    ):
        patient_age = utils.appointments.calculate_age(patient_data.birthdate)
        historical_doctor_of_speciality = patient_data.historical_doctors.get(
            found_motive.speciality_id
        )
        if (
            historical_doctor_of_speciality
            and (
                historical_doctor_of_speciality.age_minimum is None
                or patient_age >= historical_doctor_of_speciality.age_minimum
            )
            and (
                historical_doctor_of_speciality.age_maximum is None
                or patient_age <= historical_doctor_of_speciality.age_maximum
            )
            and historical_doctor_of_speciality.agenda_id
        ):
            agenda_ids_to_check = [historical_doctor_of_speciality.agenda_id]
    # Check if modifying an existing appointment
    elif phone_caller.intent == Intents.MODIFIER:
        old_appointment_form = patient_data.next_appointment
        agenda_ids_to_check = [old_appointment_form.agenda_id]

    # Check if user explicitly chose a doctor
    if (
        ask_doctor_name
        and appointment_form.agenda_id
        and phone_caller.intent != Intents.MODIFIER
    ):
        agenda_ids_to_check = [appointment_form.agenda_id]

    room_agenda_ids_per_speciality = [
        doctor.agenda_id
        for doctor in doctors
        if not doctor.practitioner_id
        and doctor.speciality_id == found_motive.speciality_id
        and doctor.agenda_id not in agenda_ids_to_check
    ]

    if room_agenda_ids_per_speciality:
        print(f"Adding room agenda ids {room_agenda_ids_per_speciality}")
        agenda_ids_to_check.extend(room_agenda_ids_per_speciality)

    # remove AJREZO from the list if motive
    if (
        company_data.config == "config88"
        and found_motive.visit_motive_id in [934386, 1419889, 12711825]
        and 1241312 in agenda_ids_to_check
    ):
        print("Removing AJREZO from the list")
        agenda_ids_to_check.remove(1241312)

    if 1234622 in agenda_ids_to_check:
        agenda_ids_to_check.remove(1234622)

    if "icpc" in company_data.config_name.lower() and appointment_form.medecin:
        agenda_ids_to_check = [
            calendar.agenda_id
            for calendar in company_data.inbound_config_file.calendars
            if appointment_form.medecin in calendar.name.lower()
        ]
        print(f"ICPC company agendas")
        print(agenda_ids_to_check)

    return agenda_ids_to_check


async def check_age_compatibilty(
    patient_data: PatientData,
    agenda_ids: List[str],
    booking_provider: BookingProvider,
) -> List[str]:
    """
    Check if the patient's age is compatible with the agenda's age requirements.

    Args:
        patient_data: PatientData object containing patient information.
        agenda_ids: List of agenda IDs to check against.
        booking_provider: BookingProvider instance to fetch doctor data.

    Returns:
        List of agenda IDs that are compatible with the patient's age.
    """
    if not patient_data.birthdate or not utils.appointments.is_valid_date(
        patient_data.birthdate
    ):
        # If birthdate is missing or invalid, return agenda IDs for doctors without age restrictions
        compatible_agenda_ids = []
        for agenda_id in agenda_ids:
            doctor = await booking_provider.get_doctor_by_agenda_id(agenda_id)
            if not doctor:
                continue
            if doctor.age_minimum is None and doctor.age_maximum is None:
                compatible_agenda_ids.append(agenda_id)
        # If none found, fallback to returning all agenda_ids (previous behavior)
        return compatible_agenda_ids if compatible_agenda_ids else agenda_ids

    patient_age = utils.appointments.calculate_age(patient_data.birthdate)
    compatible_agenda_ids = []

    for agenda_id in agenda_ids:
        doctor = await booking_provider.get_doctor_by_agenda_id(agenda_id)
        if not doctor:
            continue

        if (doctor.age_minimum is None or patient_age >= doctor.age_minimum) and (
            doctor.age_maximum is None or patient_age <= doctor.age_maximum
        ):
            compatible_agenda_ids.append(agenda_id)

    return compatible_agenda_ids


async def get_next_availabilties(
    wish_next_appointment_date: datetime,
    found_availabilities: List[Appointment],
    booking_provider: BookingProvider,
    appointment_form: AppointmentForm,
    agenda_ids_to_check: List[str],
    company_data: CompanyData = None,
    patient_data: PatientData = None,
    phone_caller: PhoneCaller = None,
):

    if isinstance(wish_next_appointment_date, date) and not isinstance(
        wish_next_appointment_date, datetime
    ):
        wish_next_appointment_date = datetime.combine(
            wish_next_appointment_date, time.min
        )
    if not getattr(wish_next_appointment_date, "tzinfo", None):
        wish_next_appointment_date = pytz.timezone("Europe/Paris").localize(
            wish_next_appointment_date
        )

    current_datetime = datetime.now(pytz.timezone("Europe/Paris"))
    earliest_allowed_date = current_datetime + timedelta(days=1)

    if company_data.config == "config119" or company_data.config_name == "somed":
        earliest_allowed_date = current_datetime

    if "icpc" in company_data.config_name.lower():
        earliest_allowed_date = current_datetime + timedelta(days=2)

    if wish_next_appointment_date < earliest_allowed_date:
        wish_next_appointment_date = earliest_allowed_date

    agenda_ids_to_check = await check_age_compatibilty(
        patient_data, agenda_ids_to_check, booking_provider
    )

    _next_availabilities: List[Appointment] = (
        await booking_provider.get_next_availabilities(
            MotiveAppointment(
                visit_motive_id=appointment_form.visit_motive_id,
                visit_motive_name=appointment_form.visit_motive_name,
                open=appointment_form.is_open_motive,
            ),
            agenda_ids_to_check,
            from_date=wish_next_appointment_date,
            min_time_difference=None,
            number_of_time_slots=None,
            steps_motives=appointment_form.steps_motives,
        )
    )

    other_motive = None

    next_availabilities_on_wish_date = (
        [
            appointment
            for appointment in _next_availabilities
            if appointment.start_date == wish_next_appointment_date.date()
        ]
        if _next_availabilities
        else []
    )

    # case where same name but different motive and doctor is not specitied
    if (
        company_data
        and company_data.config in ["config119", "config92"]
        and (not next_availabilities_on_wish_date)
    ):
        print("Trying to find other motive with same name")
        other_motives = []
        if len(agenda_ids_to_check) > 1:
            other_motives = find_motives_with_same_name(
                MotiveAppointment(
                    visit_motive_id=appointment_form.visit_motive_id,
                    visit_motive_name=appointment_form.visit_motive_name,
                    open=appointment_form.is_open_motive,
                ),
                company_data=company_data,
            )

        if other_motives:
            for other_motive in other_motives:
                if not other_motive.open:
                    continue

                logger.info(
                    f"No availabilities found for {appointment_form.visit_motive_name}, trying with other motive"
                )

                patient_data.historical_doctors = {}

                agenda_ids_to_check = get_agendas_ids_to_check(
                    company_data,
                    phone_caller,
                    appointment_form,
                    found_motive=other_motive,
                    patient_data=patient_data,
                    ask_doctor_name=False,
                )
                print(f"Agenda IDs to check: {agenda_ids_to_check}")

                _next_availabilities += await booking_provider.get_next_availabilities(
                    MotiveAppointment(
                        visit_motive_id=other_motive.visit_motive_id,
                        visit_motive_name=other_motive.visit_motive_name,
                        open=other_motive.open,
                    ),
                    agenda_ids_to_check,
                    from_date=wish_next_appointment_date,
                    min_time_difference=None,
                    number_of_time_slots=None,
                    steps_motives=appointment_form.steps_motives,
                )

    # case where doctor is not specified and have one motive
    if (
        company_data
        and company_data.config in ["config119", "config92"]
        and not appointment_form.medecin
    ):
        print("Trying to find other motive with same name and no doctor specified")
        patient_data.historical_doctors = {}
        agenda_ids_to_check = get_agendas_ids_to_check(
            company_data,
            phone_caller,
            appointment_form,
            found_motive=appointment_form.visit_motive_id,
            patient_data=patient_data,
            ask_doctor_name=False,
        )
        print(f"Agenda IDs to check: {agenda_ids_to_check}")

        _next_availabilities += await booking_provider.get_next_availabilities(
            MotiveAppointment(
                visit_motive_id=appointment_form.visit_motive_id,
                visit_motive_name=appointment_form.visit_motive_name,
                open=appointment_form.is_open_motive,
            ),
            agenda_ids_to_check,
            from_date=wish_next_appointment_date,
            min_time_difference=None,
            number_of_time_slots=None,
            steps_motives=appointment_form.steps_motives,
        )

    all_availabilities: List[Appointment] = found_availabilities + _next_availabilities

    # Deduplicate by start_date
    seen = set()
    unique_appointments: List[Appointment] = []
    for appointment in all_availabilities:

        if not isinstance(appointment, Appointment):
            logger.warning(f"Skipping non-Appointment object: {appointment}")
            continue

        start_date = appointment.start_date
        if start_date not in seen:
            seen.add(start_date)
            unique_appointments.append(appointment)

    next_availabilities = unique_appointments

    # Filter out appointments too far in the future
    if next_availabilities:
        soonest = next_availabilities[0].start_date
        days_difference = (soonest - current_datetime).days
        if days_difference > MAX_APPOINTMENT_DAYS:
            return []

    next_availabilities.sort(key=lambda x: x.start_date)

    # remove slots that are in less than 1h
    next_availabilities = [
        appointment
        for appointment in next_availabilities
        if appointment.start_date > current_datetime + timedelta(hours=1)
    ]

    return next_availabilities
