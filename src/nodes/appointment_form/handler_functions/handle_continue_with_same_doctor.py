from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from appointment_form.intake_processor import AppointmentFormIntakeProcessor

from pipecat.services.openai.llm import (
    OpenAILLMService,
    OpenAILLMContext,
)

from datetime import datetime
from nodes.appointment_form.node_functions import (
    get_agendas_ids_to_check,
    find_motive_by_id,
)


async def handle_continue_with_same_doctor(
    self: "AppointmentFormIntakeProcessor",
    function_name,
    tool_call_id,
    args,
    llm: OpenAILLMService,
    context: OpenAILLMContext,
    result_callback,
):
    continue_with_other_doctors = args.get("confirm", True)
    self.has_asked_to_change_doctor = True
    if continue_with_other_doctors:
        self.appointment_form.medecin = None
        self.appointment_form.agenda_id = None

        motive = find_motive_by_id(
            self.appointment_form.visit_motive_id, self.company_data
        )
        self.agenda_ids_to_check = get_agendas_ids_to_check(
            company_data=self.company_data,
            appointment_form=self.appointment_form,
            phone_caller=self.phone_caller,
            found_motive=motive,
            patient_data=self._patient_data,
            ask_doctor_name=self.company_data.bot_configuration.ask_name_doctor,
            check_all_agendas_ids=True,
        )

    today = datetime.now()
    # set hour and minute to 0
    today = today.replace(hour=0, minute=0, second=0, microsecond=0)
    print(f"Setting appointment datetime to today: {today}")
    await self.handle_appointment_datetime(
        function_name,
        tool_call_id,
        {"appointment_datetime": today},
        llm,
        context,
        result_callback,
    )
