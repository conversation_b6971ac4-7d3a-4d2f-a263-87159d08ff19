from typing import List, Optional, Any, Dict

from loguru import logger

from models import (
    CompanyData,
    Appoint<PERSON><PERSON><PERSON>,
    Doctor,
    Appointment,
    MotiveAppointment,
    Speciality,
    PatientData,
)
from datetime import datetime
from constants import Intents
import utils.appointments
from .constants import APPOINTMENT_MOTIVE_INTRO_SENTENCE
from ..base.prompts import BasePrompts
from .tools import AppointmentFormTools
from ..contact_form.prompts import ContactFormPrompts
from collections import defaultdict
from pytz import timezone
import random


class AppointmentFormPrompts(BasePrompts):
    def __init__(self, company_data: CompanyData, specialities: List[Speciality] = []):
        super().__init__(company_data)
        self.contact_form_prompts = ContactFormPrompts(company_data)

    def ask_birthdate_of_patient(self, return_node: Optional[dict[str, Any]] = None):
        return_node_name = return_node["function"]["name"] if return_node else ""
        message = self._set_message(
            f"""
            Demande clairement au patient : « Quelle est votre date de naissance ? »  
            ---

            Tu dois absolument obtenir la date de naissance du patient.  
            Une fois que tu l'as comprise, appelle la fonction `handle_birthdate_of_patient`.

            Le patient **ne peut pas** te poser de questions ici. Il doit simplement **répondre à ta question**.

            Si tu ne comprends pas la réponse du patient, demande-lui de répéter, avec un exemple clair :  
            « Par exemple : 29 mars 1982. »

            La date de naissance doit être convertie au **format ISO `YYYY-MM-DD`**,  
            par exemple : `1982-03-29`.
            """
        )

        tools = [
            AppointmentFormTools.handle_birthdate_of_patient,
        ]

        tools.append(return_node) if return_node else None

        return message, tools

    def ask_motive(
        self,
        main_reason: MotiveAppointment = None,
        return_node: Optional[dict[str, Any]] = None,
        first_sentence: Optional[str] = "",
        words_boost: Optional[Dict[str, str]] = {},
    ):
        if not first_sentence or len(first_sentence) == 0:
            first_sentence = APPOINTMENT_MOTIVE_INTRO_SENTENCE

        return_node_name = return_node["function"]["name"] if return_node else ""

        words_boost_arr = (
            [
                f"- Le mot '{word_said}' devrait être '{actual_word}'"
                for word_said, actual_word in words_boost.items()
            ]
            if words_boost
            else []
        )

        words_boost_str = ""
        if words_boost:
            words_boost_str = "Situation particulière :\n"
            words_boost_str += (
                "\n".join(map(str, words_boost_arr)) if words_boost_arr else ""
            )

        logger.info(f"ask_motive debug: {first_sentence}")

        if not main_reason:
            message = self._set_message(
                f"""
                🚨 Ta tâche maintenant est de dire **exactement et uniquement** cette phrase, sans modification, sans ajout, sans reformulation :
                👉 "{first_sentence}"

                Tu n'as pas le droit de changer cette phrase. C'est ta seule et unique sortie jusqu'à ce que le patient réponde.

                Ensuite, à la réponse du patient :
                - Mets `oui` **seulement et uniquement** si le patient dit clairement "oui".
                - Si le patient dit que c'est sa première fois, ajoute le mot `première <motif>`.
                - S'il précise que c'est un suivi (deuxième ou plus), utilise `suivi <motif>`.
                - Si le patient dit un motif, utilise `handle_motive` avec le motif dit par l'utilisateur
                - Si l'utilisateur a déjà dit “non” ou “autre”, **ne répète pas** ces valeurs dans `handle_motive`.

                {"- S'il souhaite revenir à la question précédente, utilise la fonction " + return_node_name + "." if return_node else ""}

                ❌ Le patient ne peut pas te poser de question.
                ❌ Tu ne dois pas appeler la fonction `handle_speciality` car la spécialité est déjà définie.
                ❌ Tu ne dois en aucun cas dire que le rendez-vous est confirmé car il y a encore des étapes à suivre.

                {words_boost_str}
                """
            )

        else:
            main_reason_name = main_reason.visit_motive_name.replace(
                "(tous)", ""
            ).strip()
            if "enfant" in main_reason_name.lower():
                main_reason_name = "un" + main_reason_name
            else:
                main_reason_name = "une" + main_reason_name

            first_sentence = (
                f"Souhaitez-vous prendre rendez-vous pour {main_reason_name}?"
                if main_reason_name
                else APPOINTMENT_MOTIVE_INTRO_SENTENCE
            )

            message = self._set_message(
                f"""
                🚨 Ta tâche maintenant est de dire **exactement et uniquement** cette phrase, sans modification, sans ajout, sans reformulation :
                👉 "{first_sentence} Souhaitez-vous prendre rendez-vous pour {main_reason_name} ?"

                Tu n'as pas le droit de changer cette phrase. C'est ta seule et unique sortie jusqu'à ce que le patient réponde.

                Ensuite, à la réponse du patient :
                - Si le patient répond 'oui', mets `oui` et appelle la fonction `handle_motive` avec `oui`.
                - Si le patient précise que c'est sa première fois, ajoute le mot `première <motif>`.
                - S'il précise que c'est un suivi (deuxième ou plus), utilise `suivi <motif>`.
                - Si le patient dit un motif, utilise `handle_motive` avec le motif dit par l'utilisateur
                - Si l'utilisateur a déjà dit “non” ou “autre”, **ne répète pas** ces valeurs dans `handle_motive`.

                {"- S'il souhaite revenir à la question précédente, utilise la fonction " + return_node_name + "." if return_node else ""}

                ❌ Le patient ne peut pas te poser de question.
                ❌ Tu ne dois pas appeler la fonction `handle_speciality` car la spécialité est déjà définie.
                ❌ Tu ne dois en aucun cas dire que le rendez-vous est confirmé car il y a encore des étapes à suivre.

                {words_boost_str}
                """
            )

        tools = [
            AppointmentFormTools.handle_motive,
        ]

        return message, tools

    def ask_confirm_reason_motive(
        self,
        reason_motive: str,
        intent: Intents,
        return_node: Optional[dict[str, Any]] = None,
    ):
        return_node_name = return_node["function"]["name"] if return_node else ""
        if "enfant" in reason_motive.lower():
            reason_motive = "un " + reason_motive
        else:
            reason_motive = "une " + reason_motive

        if intent == Intents.MODIFIER:
            msg = "Souhaitez-vous modifier ce rendez-vous?"
        elif intent == Intents.NOUVEAU:
            msg = f"Souhaitesz-vous prendre rendez-vous pour {reason_motive}?"
        elif intent == Intents.ANNULATION:
            msg = "Souhaitez-vous annuler ce rendez-vous"
        else:
            msg = f"Souhaitez-vous prendre rendez-vous pour {reason_motive}?"

        message = self._set_message(
            f"""
              🚨 Ta tâche est de **dire cette phrase en corrigeant uniquement la grammaire ou la ponctuation si nécessaire**, sans changer les mots, sans reformuler, et sans en altérer le sens :

              👉 "{msg}"

            
              ⛔️ Tu ne dois **pas** :
              - Changer des mots.
              - Réorganiser la phrase.
              - Ajouter du contexte.
              - Reformuler ou reformuler partiellement.

              Tu dois **respecter strictement le contenu de la phrase**, en appliquant uniquement des corrections grammaticales si besoin.

              ---
              Après que le patient ait parlé :

              - S'il répond 'oui' ou 'non', utilise la fonction `handle_confirm_motive` avec True (si oui) ou False (si non).
              - 🚨 N'appelle `handle_confirm_motive` qu'avec le paramètre `confirm` et n'ajoute pas d'autres paramètres.
              - S'il demande à être transféré, utilise `forward_call`.

              🛑 Le patient ne peut pas poser de question. Il doit simplement répondre.
              """
        )

        tools = [
            AppointmentFormTools.handle_confirm_motive,
            AppointmentFormTools.forward_call,
        ]
        tools.append(return_node) if return_node else None

        return message, tools

    def ask_if_injection(self, return_node: Optional[dict[str, Any]] = None):
        return_node_name = return_node["function"]["name"] if return_node else ""

        message = self._set_message(
            f"""
              Dis: "Un produit de contraste par injection est-il prescrit sur votre ordonnance ?"
              ---
              Après que le patient ait parlé :

              - S'il répond 'oui' utilise la fonction `handle_check_if_injection` avec `oui`.
              - S'il répond 'non' utilise la fonction `handle_check_if_injection` avec `non`.
              {"- S'il souhaite revenir à la question précédente, utilise la fonction " + return_node_name + "." if return_node else ""}
              - S'il ne répond pas à la question, explique gentiment que tu n'as pas compris et repose la question

              🛑 Le patient ne peut pas poser de question. Il doit simplement répondre.
              """
        )

        tools = [AppointmentFormTools.handle_check_if_injection]
        tools.append(return_node) if return_node else None

        return message, tools

    def ask_for_injection_allergies(self, return_node: Optional[dict[str, Any]] = None):
        message = self._set_message(
            f"""
              Dis: "Avez-vous une allergie au produit de contraste iodé ?"
              ---
              Après que le patient ait parlé :

              - S'il répond 'oui' ou semble hésitant, utilise la fonction `handle_check_for_injection_allergies` avec `oui`.
              - S'il répond 'non' utilise la fonction `handle_check_for_injection_allergies` avec `non`.
              - S'il ne répond pas à la question, explique gentiment que tu n'as pas compris et repose la question

              🛑 Le patient ne peut pas poser de question. Il doit simplement répondre.
              """
        )

        tools = [AppointmentFormTools.handle_check_for_injection_allergies]
        tools.append(return_node) if return_node else None

        return message, tools

    def ask_for_mri_counter_indications(
        self, return_node: Optional[dict[str, Any]] = None
    ):
        message = self._set_message(
            f"""
              Dis: "Avez-vous un objet métallique dans le corps ? Par exemple un pacemaker ou une valve cardiaque ?"
              ---
              Après que le patient ait parlé :

              - S'il répond 'oui' ou semble hésitant, utilise la fonction `handle_check_for_mri_counter_indications` avec `oui`.
              - S'il répond 'non' utilise la fonction `handle_check_for_mri_counter_indications` avec `non`.
              - S'il ne répond pas à la question, explique gentiment que tu n'as pas compris et repose la question

              🛑 Le patient ne peut pas poser de question. Il doit simplement répondre.
              """
        )

        tools = [AppointmentFormTools.handle_check_for_mri_counter_indications]
        tools.append(return_node) if return_node else None

        return message, tools

    def ask_doctor_name(
        self,
        doctors: List[Doctor],
        main_doctor: Doctor = None,
        specific_speciality: Optional[Speciality] = None,
        return_node: Optional[dict[str, Any]] = None,
    ):

        specialities = self.company_data.inbound_config_file.specialities
        doctors_by_speciality: Dict[str, List[Doctor]] = defaultdict(list)
        specialities = self.company_data.inbound_config_file.specialities

        for doctor in doctors:
            speciality = next(
                (spec for spec in specialities if spec.id == doctor.speciality_id),
                None,  # <- fix: provide default value
            )
            speciality_name = speciality.name if speciality else "Autre"
            doctors_by_speciality[speciality_name].append(doctor)

        doctors_str = ""
        for speciality, doctors_list in doctors_by_speciality.items():
            doctors_names = ", ".join(
                f"{doctor.name}" for doctor in doctors_list if doctor.name
            )
            doctors_str += f"{speciality}: {doctors_names}\n"

        return_node_name = return_node["function"]["name"] if return_node else ""

        sentence = (
            f"Souhaitez-vous prendre rendez-vous avec Docteur {main_doctor.name.lower().split('(')[0]}?"
            if main_doctor
            else "Avec quel docteur souhaitez-vous prendre ce rendez-vous ?"
        )

        if specific_speciality:
            sentence = f"Souhaitez-vous prendre rendez-vous avec Docteur {main_doctor.name.lower().split('(')[0]} de la spécialité {specific_speciality.name.lower()}?"

        message = self._set_message(
            f"""
            Dis : '{sentence}'
            ---
            Après que le patient a parlé:
            Utilise la fonction 'handle_doctor_name' avec le nom du docteur dit par le patient.

            Le nom doit correspondres à l'un des noms de la liste. Si le nom mentionné ne correspond mets juste le nom du docteur en valeur. Il peut également dire 'non' si le choix du docteur n'est pas important.

            - S'il dit 'non', utilise 'handle_doctor_name' avec la valeur 'non'.
            - S'il dit 'pas de préférence', 'peu importe', 'autre' ou 'je ne sais pas' utilise 'handle_doctor_name' avec la valeur 'autre'.
            - S'il dit 'je ne sais pas', utilise 'handle_doctor_name' avec la valeur 'autre'.
            - S'il dit 'oui', utilise 'handle_doctor_name' avec la valeur 'oui'

            Si et seulement si le patient te demande, voici la liste des docteurs, ne génère pas de markdown, mais donne la liste des docteurs avec leurs spécialités et noms en miniuscules : 
            {doctors_str}
            Tu diras comme suit:
            Pour la spécialité 'spécialité', les docteurs disponibles sont : docteur1, docteur2, docteur3.
            etc...
            
            
            Le patient ne peut pas te poser de question. Il doit simplement répondre à ta question concernant le nom du docteur.
            Tu n'as pas le droit de confirmer le rendez-vous à cette étape, car il y a encore des étapes à suivre.
            Si la question est répétée car c'est pour confirmé le nom du docteur, utilise la fonction 'handle_doctor_name' avec la valeur 'oui' ou 'non' selon la réponse du patient.
            
            {"S'il souhaite évoquer un autre motif, utilise la fonction " + return_node_name + "." if return_node else ""}
            """
        )
        print(doctors_str)

        tools = [
            AppointmentFormTools.handle_doctor_name,
        ]

        tools.append(return_node) if return_node else None

        return message, tools

    def suggest_appointment_datetime(
        self,
        closer_datetimes: List[Appointment],
        with_doctor: bool = False,
        return_node: Optional[dict[str, Any]] = None,
        first_sentence: str = "",
        wish_appointment_datetime: Optional[datetime] = None,
    ):

        is_same_doctor = (
            len(set(appointment.agenda_id for appointment in closer_datetimes)) == 1
        )

        if not is_same_doctor:
            with_doctor = True

        closer_datetimes = sorted(
            closer_datetimes, key=lambda x: x.start_date, reverse=False
        )
        closer_datetimes_str = [
            (
                utils.appointments.format_appointment_to_datetime_with_day_in_french(
                    time_slot
                )
                if with_doctor and not is_same_doctor
                else utils.appointments.format_datetime_with_day_in_french(
                    time_slot.start_date
                )
            )
            for time_slot in closer_datetimes
        ]

        with_same_doctor_name_str = ""
        if (
            is_same_doctor
            and with_doctor
            and len(closer_datetimes[0].medecin.split(" ")) > 1
            and "radiolog" not in closer_datetimes[0].medecin.lower()
            and "icpc" not in self.company_data.config_name.lower()
        ):
            with_same_doctor_name_str = (
                " avec docteur "
                + closer_datetimes[0].medecin.split("(")[0].strip().lower()
            )

        if (
            is_same_doctor
            and with_doctor
            and "icpc" in self.company_data.config_name.lower()
        ):
            with_same_doctor_name_str = (
                " à ICPC " + closer_datetimes[0].medecin.split("-")[-1].strip().lower()
            )

        return_node_name = return_node["function"]["name"] if return_node else ""

        wish_not_available_sentence = ""
        # Construction du message à dire
        if wish_appointment_datetime:
            wish_found = wish_appointment_datetime.date() in [
                appointment.start_date.date() for appointment in closer_datetimes
            ]
            if not wish_found:
                wish_not_available_sentence = f"Il n'y a pas de disponibilité pour le {wish_appointment_datetime.strftime('%d %B')} "

        if isinstance(closer_datetimes_str, list) and len(closer_datetimes_str) > 1:
            sentence_to_say = f"{first_sentence}. {wish_not_available_sentence}Deux de nos disponibilités les plus proches{with_same_doctor_name_str} sont : {' ou '.join(closer_datetimes_str)}. Dites-moi quel créneau vous convient."
        elif isinstance(closer_datetimes_str, list) and len(closer_datetimes_str) == 1:
            sentence_to_say = f"{first_sentence}. {wish_not_available_sentence}Notre disponibilité la plus proche{with_same_doctor_name_str} est : {' ou '.join(closer_datetimes_str)}. "
        else:
            sentence_to_say = f"{first_sentence}. {wish_not_available_sentence}Veuillez me donner une autre date ou un autre jour de la semaine{with_same_doctor_name_str}."

        print(sentence_to_say)
        today = datetime.now(timezone("Europe/Paris")).strftime("%A %Y-%m-%d")
        # Prompt final
        message = self._set_message(
            f"""
              aujourd'hui c'est {today}.
              
              🗣️ Tu dois prononcer **exactement** cette phrase, sans aucun changement, ajout ou reformulation :
              👉 "{sentence_to_say}"

              ❌ Tu ne dois rien faire d'autre.  
              ❌ Tu ne dois pas analyser ou réagir à une réponse.  
              ❌ Tu ne dois pas appeler de fonction.

              ✅ Tu dois attendre la réponse du patient avant toute autre action.
              ✅ *Après que le patient a parlé, tu dois *tout de suite* appeler la fonction `handle_appointment_datetime` systématiquement avec les champs appointment_datetime et si nécessaire appointment_day*
              """
        )
        tools = [
            AppointmentFormTools.handle_appointment_datetime,
        ]

        return message, tools

    def suggest_a_day(self, already_asked: bool = False, first_sentence: str = ""):
        today = datetime.now().strftime("%A, %d %B %Y")
        message = self._set_message(
            f"""
                🚨 Tu dois prononcer **exactement** cette phrase, sans aucun changement, ajout ou reformulation, et sans modifier le nom du docteur :
                👉 "Dites-moi simplement à quel moment vous seriez disponible, je vais m'adapter !"

                Ensuite, en fonction de la réponse du patient, tu dois **appeler systématiquement** la fonction `handle_appointment_datetime` selon les règles suivantes :

                ✅ Réponses du patient :
                - "aucun", "encore", "non", "autre" → `handle_appointment_datetime("non")`
                - "oui", "confirme" → utilise la **dernière date et heure proposée** (format `YYYY-MM-DD HH:MM`)
                - Propose une autre **heure** → utilise la **dernière date proposée** avec l'**heure demandée**
                - Propose une autre **date** → utilise la **date demandée** avec l'heure **00:00**
                - Donne un **jour de la semaine** ("lundi", etc.) → `handle_appointment_datetime(appointment_day="lundi", appointment_datetime=None)`
                  ❌ Ne complète **pas** `appointment_datetime` avec une date ou heure actuelle
                - Donne un **mois** ("janvier", etc.) → interprète comme le **1er du mois** à **00:00** (ex: `'2025-01-01 00:00'`)
                - Demande "matin" → `handle_appointment_datetime(`YYYY-MM-DD 08:00, with_half_matin_aprem=True)`
                - Demande "après-midi" → `handle_appointment_datetime(`YYYY-MM-DD 14:00, with_half_matin_aprem=True)`

                ❌ Ne confirme **jamais** le rendez-vous à ce stade.
                ❌ Si le patient mentionne le secrétariat :
                  - Réponds que **la prise de rendez-vous se fait uniquement via l'assistant virtuel**
                  - Tu ne peux pas laisser de message ou transférer au secrétariat

                ❌ Tu ne dois **jamais** appeler `handle_appointment_day`
                
                👉 Au lieu de dire "Votre rendez-vous est confirmé pour YYYY-MM-DD HH:MM", tu dois appeler `handle_appointment_datetime` avec la date et l'heure souhaitée.
                👉 Tu dois **toujours** appeler `handle_appointment_datetime` après la réponse du patient.
                """
        )

        tools = [
            AppointmentFormTools.handle_appointment_datetime,
        ]

        return message, tools

    def suggest_times(
        self,
        wish_date: datetime,
        suggested_timeslots: List[Appointment],
        with_doctor_name: bool = True,
    ):
        """
        Args:
            wish_day: str representing the day of the week in French (lundi, mardi, etc.)
            suggested_timeslots: list of time slots available
        """

        wish_appointments = utils.appointments.get_closest_appointments(
            suggested_timeslots, wish_date
        )

        if len(wish_appointments) > 0:
            wish_appointments.sort(key=lambda x: x.start_date, reverse=False)
            dates_str = [
                (
                    utils.appointments.format_appointment_to_datetime_with_day_in_french(
                        wish_appointment
                    )
                    if with_doctor_name
                    else utils.appointments.format_datetime_with_day_in_french(
                        wish_appointment.start_date
                    )
                )
                for wish_appointment in wish_appointments
            ]
            message = self._set_message(
                f"""
                    Dit: Voici les horaires disponibles: {" ou ".join(dates_str)}
                    --
                    Ici tu n'as PAS le droit de confirmer le rendez-vous ! Car il y a encore des étapes à suivre.
                    Appelle la fonction 'handle_appointment_time' avec la date et time souhaité pour continuer. 
                    Si la date n'est pas précisé, utilise la fonction 'handle_appointment_time' avec la même date déjà proposée et la time souhaité pour continuer.
                    Si le client dit 'aucun', 'non', 'autre', utilise la fonction 'handle_appointment_datetime' avec la valeur 'autre' pour continuer. 
                    Si le client donne une date et heure précise, utilise la fonction 'handle_appointment_time' et remplis les deux champs.
                    Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant les horaires.
                    """
            )
        else:
            suggested_timeslots = utils.appointments.get_closest_appointments(
                suggested_timeslots, wish_date, False, 24
            )

            dates_str = [
                (
                    utils.appointments.format_appointment_to_datetime_with_day_in_french(
                        suggest_timslow
                    )
                    if with_doctor_name
                    else utils.appointments.format_datetime_with_day_in_french(
                        suggest_timslow.start_date
                    )
                )
                for suggest_timslow in suggested_timeslots
            ]

            message = self._set_message(
                f"""
                    Dit: Voici nos horaires disponibles les plus proche: {" ou ".join(dates_str)}
                    --
                    Ici tu n'as PAS le droit de confirmer le rendez-vous ! Car il y a encore des étapes à suivre.
                    Appelle la fonction 'handle_appointment_time' avec la date et time souhaité pour continuer.
                    Si le client dit 'aucun', 'non', 'autre', utilise la fonction 'handle_appointment_datetime' avec la valeur 'autre' pour continuer. 
                    Si le client donne une date et heure précise, utilise la fonction 'handle_appointment_time' et remplis les deux champs.
                    Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant les horaires.
                    """
            )

        tools = [
            AppointmentFormTools.handle_appointment_time,
            AppointmentFormTools.forward_call,
        ]

        return message, tools

    """
    Prompt pour situation retard
    """

    def ask_appointment_confirmation(
        self, appointment: AppointmentForm, intent: str, with_doctor_name: bool = True
    ):

        if isinstance(intent, Intents):
            if intent == Intents.MODIFIER:
                intent = "modification"
            elif intent == Intents.NOUVEAU:
                intent = "nouveau"
            elif intent == Intents.ANNULATION:
                intent = "annulation de votre"
            elif intent == Intents.RETARD:
                intent = "retard de votre"
            elif intent == Intents.CONFIRMER:
                intent = ""

        # Vérifier si c'est un rendez-vous avec plusieurs examens
        if appointment.steps and len(appointment.steps) > 1:
            # Formatter pour plusieurs examens
            first_step = appointment.steps[0]
            # second_step = appointment.steps[1] if len(appointment.steps) > 1 else None

            first_start = first_step.get("start_date")
            if isinstance(first_start, str):
                first_start = datetime.fromisoformat(first_start)

            date_str = (
                utils.appointments.format_datetime_with_day_in_french(first_start)
                .split("(")[0]
                .strip()
            )

            # Simplifier le message pour éviter les répétitions
            if intent == Intents.MODIFIER.value:
                intent = "nouveau"
            elif intent == Intents.ANNULATION:
                intent = "annulation de vos"
            elif intent == Intents.RETARD:
                intent = "retard de vos"

            message = self._set_message(
                f"""
                🎯 Ta tâche maintenant est de **dire exactement et uniquement** la phrase suivante, sans modification, reformulation ni ajout :
                👉 "Confirmez-vous votre {intent} rendez-vous du {date_str} pour vos deux examens ?"

                Tu **dois** commencer ta réponse par cette phrase, sans rien ajouter ni enlever.
                
                Ensuite, après la réponse du patient :
                - Si le patient dit "oui", appelle `handle_appointment_confirmation(True)`.
                - Si le patient dit "non", appelle `handle_appointment_confirmation(False)`.
                {"- S'il dit non, utilise aussi `transfer_call`." if intent == Intents.RETARD else ""}
                - Si la réponse est ambiguë, répète **exactement** la même question.

                ❌ Le patient ne peut poser aucune question.
                ✅ Il doit seulement dire "oui" ou "non".
                """
            )
        else:
            # Comportement original pour un seul examen
            appointment_text_time = (
                (
                    utils.appointments.format_datetime_with_day_in_french(
                        appointment.start_date
                    )
                    if with_doctor_name
                    else utils.appointments.format_datetime_with_day_in_french(
                        appointment.start_date
                    )
                )
                .split("(")[0]
                .strip()
            )  # Assurez-vous que le texte de l'heure du rendez-vous ne donne que la date, pas l'heure

            if intent == Intents.MODIFIER.value:
                intent = "nouveau"

            # Pour un seul examen, on peut mentionner le motif
            if (
                appointment.visit_motive_id == 354572
                and self.company_data.config == "config87"
            ):
                appointment.visit_motive_name = "une consultation de cardiologie"

            message = self._set_message(
                f"""
                🚨 Ignore tout ce qui a été dit auparavant.

                🗣️ Tu dois commencer ta réponse en disant **exactement et uniquement** la phrase suivante, sans modification, sans ajout, sans reformulation :
                👉 "Confirmez-vous votre {intent} rendez-vous du {appointment_text_time} pour {appointment.visit_motive_name} ?"

                Ensuite, après que le patient a parlé :
                - Si le patient dit **"oui"** ou **"ok"**, appelle `handle_appointment_confirmation(True)`.
                - Si le patient dit **"non"**, appelle `handle_appointment_confirmation(False)`.
                {"- Si le patient dit non, utilise aussi `transfer_call`." if intent == Intents.RETARD else ""}
                - Si la réponse n'est pas claire, répète **exactement** la même question, sans la modifier.

                ❌ Le patient ne peut pas te poser de questions.
                ✅ Il peut seulement dire **"oui"** ou **"non"**.
                """
            )

        tools = [
            AppointmentFormTools.handle_appointment_confirmation,
        ]

        tools = [tool for tool in tools if tool is not None]
        return message, tools

    def ask_confirm_appointment_metadata(self, last_appointment: AppointmentForm):
        """ "
        Si le client veut confirmer le rendez-vous (oui sur doctolib dans la metadata)
        """
        appointment_str = (
            utils.appointments.format_appointment_to_datetime_with_day_in_french(
                last_appointment
            ).replace("Le", "")
        )
        message = self._set_message(
            f"""
            Dit: Voulez-vous confirmer votre rendez-vous du {appointment_str} ?
            ---
            Si dit oui, utilise la fonction handle_confirm_appointment_metadata.
            Si dit non, transfère l'appel à un humain.
            """
        )

        tools = [
            AppointmentFormTools.handle_confirm_appointment_metadata,
            AppointmentFormTools.forward_call,
        ]

        return message, tools

    def ask_reason_of_cancellation(
        self, intent: Intents = Intents.ANNULATION, steps=None
    ):
        vos = "vos" if steps and len(steps) > 1 else "votre"
        message = self._set_message(
            f"""
              🚨 Ta prochaine phrase doit être **exactement cette phrase**, sans la modifier, sans reformuler, sans ajouter de contexte :
              👉 'Pour quelle raison souhaitez-vous {intent.value} {vos} rendez-vous ?'

              ---
              Ensuite, quand le patient répond tu dois toujours :
              - Utiliser la fonction `handle_reason_of_cancellation` avec la raison de l'annulation ou de la modification du rendez-vous qu'il a donnée.

              🛑 Le patient **ne peut pas** te poser de question.
              ✅ Il doit simplement répondre à ta question concernant la raison de l'annulation ou de la modification.
              """
        )

        tools = [
            AppointmentFormTools.handle_reason_of_cancellation,
        ]

        return message, tools

    def ask_more_information_for_motive(self, appointment_form: AppointmentForm):

        motive_name = appointment_form.visit_motive_name
        message = self._set_message(
            f"""
            Dit: 'Avant que je puisse confirmer votre rendez-vous, pouvez-vous me dire pourquoi vous souhaitez prendre {motive_name}?'
            ---
            Utilise la fonction 'handle_more_information_for_motive' avec la reason du pourquoi la personne veut prendre ce rendez-vous 
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question avec la raison du rendez-vous
            """
        )

        tools = [
            AppointmentFormTools.handle_more_information_for_motive,
        ]

        return message, tools

    def ask_with_echo_mammaire(self, motive: MotiveAppointment):

        motif_name = (
            "Échographie mammaire"
            if "mammographie" in motive.visit_motive_name.lower()
            else "Mammographie"
        )

        message = self._set_message(
            f"""
            Dit: 'Est-ce qu'une {motif_name} est également demandé sur votre ordonnance?'
            ---
            Utilise la fonction 'handle_with_echo_mammaire' avec "oui" ou "non" dans le champ "confirm"
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant la mammographie.
            """
        )

        tools = [
            AppointmentFormTools.handle_with_echo_mammaire,
        ]

        return message, tools

    def ask_motive_condition(self, condition_msg: str):
        message = self._set_message(
            f"""
            Dit: '{condition_msg}'
            ---
            Utilise la fonction 'handle_motive_condition' avec "oui" ou "non" dans le champ "confirm"
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant le cardiologue.
            """
        )
        tools = [
            AppointmentFormTools.handle_motive_condition,
        ]

        return message, tools

    def ask_continue_with_same_doctor(self, doctor: Doctor):
        doctor_name = doctor.name.lower().split("(")[0].strip()

        sentence = f"Le docteur {doctor_name} n'a malheureusement pas de disponibilité plus tôt. Souhaitez-vous prendre rendez-vous avec un autre praticien du centre pour une date plus proche ?"

        if "icpc" in self.company_data.config_name.lower():
            etablissement = doctor_name.split("-")[-1].strip().lower()
            sentence = f"ICPC {etablissement} n'a malheureusement pas de disponibilité plus tôt. Souhaitez-vous prendre rendez-vous avec un autre établissement pour une date plus proche ?"

        message = self._set_message(
            f"""
            Dit: '{sentence}'
            ---
            Utilise la fonction 'handle_continue_with_same_doctor' avec "oui" ou "non" dans le champ "confirm"
            Ici le patient ne peut pas te poser de question. Il doit répondre à ta question concernant le docteur.
            """
        )

        tools = [
            AppointmentFormTools.handle_continue_with_same_doctor,
        ]

        return message, tools

    def ask_how_long_late(
        self,
        is_identified: bool = False,
    ):

        if is_identified:
            sentence = "De combien de temps est votre retard ?"
        else:
            sentence = (
                "Pouvez-vous me dire de combien de temps sera votre retard ? "
                "Et pouvez-vous aussi me donner votre prénom et nom de famille pour confirmer votre identité ?"
            )
        message = self._set_message(
            f"""
            👉 Dis : **"{sentence}"**

            ---
            
            ✅ Dès que le patient donne une estimation de son retard, appelle la fonction :
            `handle_how_long_late(minutes_late=<durée en minutes>)`

            ❌ Ne demande pas autre chose.
            ❌ Ne reformule pas la question.
            """
        )

        tools = [AppointmentFormTools.handle_how_long_late]

        return message, tools

    def ask_speciality(
        self,
        specialities: List[Speciality],
        patient_data: Optional[PatientData] = None,
        return_node: Optional[dict[str, Any]] = None,
    ):
        return_node_name = return_node["function"]["name"] if return_node else ""

        # Start with all specialities
        all_specialities = specialities
        specialities_to_ask = [speciality.name for speciality in all_specialities]

        # If historical doctors exist, filter to those specialities
        if patient_data and patient_data.historical_doctors:
            speciality_ids_from_history = set(patient_data.historical_doctors.keys())
            filtered_specialities = [
                speciality
                for speciality in all_specialities
                if speciality.id in speciality_ids_from_history
            ]
            if filtered_specialities:
                specialities_to_ask = [s.name for s in filtered_specialities]

        # Prepare strings
        def clean_speciality_names(names: List[str]) -> str:
            return (
                ", ".join(names)
                .lower()
                .replace("chirurgien-dentiste", "dentisterie, orthodontie")
                .replace("médecin généraliste", "généraliste")
            )

        specialities_to_ask_str = clean_speciality_names(specialities_to_ask)

        # Message depending on number of specialities
        if len(specialities_to_ask) > 7:
            random_specialities = random.sample(specialities_to_ask, 3)
            example_str = ", ".join(random_specialities)
            msg = f"Dit: Vers quelle spécialité médicale puis-je vous orienter ? Par exemple dites: {example_str} ?"
        elif len(specialities_to_ask) < 3:
            # Add more examples from all_specialities
            remaining = [
                s.name for s in all_specialities if s.name not in specialities_to_ask
            ]
            specialities_to_ask += remaining[:3]
            specialities_to_ask_str = clean_speciality_names(specialities_to_ask)
            msg = f"Dit: Vers quelle spécialité médicale puis-je vous orienter ? Par exemple dites: {specialities_to_ask_str} ?"
        else:
            msg = f"Dit: Pour quelle spécialité appelez-vous : {specialities_to_ask_str} ?"

        # Handle words boost
        words_boost = self.company_data.bot_configuration.words_boost_prompt
        words_boost_str = ""
        if words_boost:
            corrections = [
                f"- Le mot '{wrong}' devrait être '{correct}'"
                for wrong, correct in words_boost.items()
            ]
            words_boost_str = "Situation particulière :\n" + "\n".join(corrections)

        # Final message
        message = self._set_message(
            f"""
            👉 Dis : **"{msg}"**
            
            --
            Après que le patient a répondu à ta question, tu dois :
            Si tu as compris la spécialité, utilise la fonction 'handle_speciality' pour continuer.
            Si l'utilisateur veut être transféré, utilise la fonction 'forward_call'.
            {"Si le patient souhaite retourner à la question précédente, utilise la fonction " + return_node_name if return_node else ""}
            **NE DIS PAS TOUTES LES SPÉCIALITÉS, juste celles données avant le --**:
            """
        )

        tools = [
            AppointmentFormTools.handle_speciality,
            AppointmentFormTools.forward_call,
        ]
        if return_node:
            tools.append(return_node)

        return message, tools

    def ask_how_many_motives(self):
        """
        Ask the user how many motives they have for the appointment.
        """
        message = self._set_message(
            """
            Dit: 'Souhaitez-vous un seul ou deux examens ?'
            --
            utilise la fonction 'handle_how_many_motives' pour continuer.
          """
        )

        tools = [AppointmentFormTools.handle_how_many_motives]
        return message, tools
