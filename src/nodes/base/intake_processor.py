from abc import ABC, abstractmethod
import asyncio
from typing import Any, List, Optional, Callable, Tuple
from loguru import logger
from twilio.twiml.voice_response import VoiceResponse
from pipecat.services.openai.llm import OpenAILLMService, OpenAILLMContext
from pipecat.services.openai.base_llm import (
    LLMUpdateSettingsFrame,
)
from openai import NOT_GIVEN
from pipecat.processors.frame_processor import FrameDirection
from pipecat.frames.frames import <PERSON><PERSON>rame, TTSSpeakFrame
from audios import AUDIO_TRANSFERT_CALL
from constants import (
    Intents,
    TWILIO_ACCOUNT_SID,
    TWILIO_AUTH_TOKEN,
    MAX_RETRIES_TAKE_APPOINTMENT,
    DEV_CALL_ID,
)
import json
from lib.twilio_client import TwilioClient
from models import PatientData, PhoneCaller, CompanyData, AppointmentForm, Appointment
from audios import load_sounds
from datetime import datetime
import utils.tts
from .prompts import BasePrompts
from lib.booking_provider import BookingProviderFactory


class BaseIntakeProcessor(ABC):
    def __init__(
        self,
        company_data: CompanyData,
        phone_caller: PhoneCaller,
        patient_data: PatientData,
    ):
        self.phone_caller = phone_caller
        self.company_data = company_data
        self._patient_data = patient_data
        self.phone_caller.start_time = datetime.now()
        self.prompts = BasePrompts(company_data)

        """Variables used for child classes"""

        self.INTENT_LOOKUP = {i.value: i for i in Intents}
        self.appointment_form = AppointmentForm(config=company_data.config)
        self.next_availabilities: List[Appointment] = []
        self.retries_take_appointment = 0

        """"""

        self.booking_provider = BookingProviderFactory.get_provider(company_data)
        self.sounds = load_sounds()
        self.twilio_client = TwilioClient(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)

        if DEV_CALL_ID not in self.phone_caller.id:
            self._patient_data.phone_number = (
                self.twilio_client.fetch_caller_phone_number(self.phone_caller.id)
            )

    @abstractmethod
    def __init_nodes__(self) -> List[Tuple[str, Callable]]:
        pass

    async def _reply_to_user(
        self,
        result_callback,
        context: OpenAILLMContext,
        llm: OpenAILLMService,
        message: str,
        tools: List[dict[str, Any]] = None,
    ):
        if tools:
            context.set_tools(tools)
        else:
            logger.warning("No tools provided to reply_to_user")
            await llm.push_frame(
                LLMUpdateSettingsFrame(
                    settings={"extra": {"parallel_tool_calls": NOT_GIVEN}}
                )
            )

        await result_callback({"role": "system", "content": message})
        if not tools:
            await llm.push_frame(
                LLMUpdateSettingsFrame(
                    settings={"extra": {"parallel_tool_calls": False}}
                )
            )

    async def _say_and_wait(
        self,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        message: str,
        sleep_time: Optional[int] = None,
        no_wait: bool = False,
    ):
        if sleep_time is None:
            sleep_time = utils.tts.estimate_tts_duration(message)

        message = ". ".join(message.split("\n")).strip()
        await llm.push_frame(TTSSpeakFrame(message))
        if no_wait:
            return

        await asyncio.sleep(sleep_time)

    async def forward_call(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        """
        Forward the call to the company.
        The call is forwarded to the number specified in the forward_number parameter.
        This function ends the flow after forwarding.
        if self.all_in_task is True, it means that we don't forward the call but leave a note.
        """
        force_forward = args.get("force_forward", False)
        forward_number = args.get("forward_number") or self.company_data.forward_number

        if self.phone_caller.intent == Intents.URGENCE:
            await self._say_and_wait(
                llm,
                context,
                "En cas d'urgence vitale, appelez directement le 15. Sinon, pouvez-vous décrire votre urgence.",
            )

        if (
            (self.company_data.bot_configuration.all_in_task and not force_forward)
            or not forward_number
            or forward_number == ""
        ):
            if not forward_number or forward_number == "":
                logger.warning(
                    f"Forward number is not set for company {self.company_data.name}"
                )

            await self._say_and_wait(
                llm,
                context,
                "Personne n'est disponible pour le moment,\n\nvariable 1 : j'informe le secrétariat de votre appel\n\nvariable 2 mieux : Pouvez-vous m'indiquer pourquoi vous souhaitez être mis en relation avec le secrétariat  ?",
            )
            self.phone_caller.has_note = True
            self.phone_caller.successful_call = True
            prompt, tools = self.prompts.ask_last_question()
            await self._reply_to_user(result_callback, context, llm, prompt, tools)
            return

        self.phone_caller.successful_call = True

        await self._say_and_wait(
            llm,
            context,
            "Je vais vous transférer vers le secrétariat. Il est possible que tout le personnel d'accueil soit momentanément indisponible et que l'appel se termine sans réponse. Si c'est le cas, n'hésitez pas à réessayer un peu plus tard.",
        )

        if self.phone_caller.has_forward_call:
            logger.info("Call already forwarded, ignoring repeated forward attempts.")
            return

        try:
            self.phone_caller.has_forward_call = True
            response = VoiceResponse()
            response.play(AUDIO_TRANSFERT_CALL)
            response.dial(forward_number, timeout=600)
            twiml_response = str(response)
            updated_call = await asyncio.to_thread(
                self.twilio_client.get_client().calls(self.phone_caller.id).update,
                twiml=twiml_response,
            )
            logger.info(
                f"Call forwarded to {forward_number}. Updated call status: {updated_call.status}"
            )

            return
        except Exception as e:
            logger.error(f"Failed to play audio or forward call: {str(e)}")
            await llm.push_frame(EndFrame(), FrameDirection.DOWNSTREAM)

        if self.company_data.is_test:
            await llm.push_frame(EndFrame(), FrameDirection.DOWNSTREAM)
            await llm.push_frame(EndFrame(), FrameDirection.UPSTREAM)
            return

    async def end_call(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
        ask_last_question: bool = True,
        return_node=None,
    ):
        """
        Last node of the flow. Ends the call.
        """
        context.set_tool_choice("none")

        if self.company_data.is_test:
            await llm.push_frame(EndFrame(), FrameDirection.DOWNSTREAM)
            await llm.push_frame(EndFrame(), FrameDirection.UPSTREAM)
            return

        msg = "Merci pour votre appel. Je vous souhaite une excellente journée !"
        if ask_last_question:
            msg = "Avez-vous d'autres questions ?"

        if (
            hasattr(self, "retries_take_appointment")
            and self.retries_take_appointment < MAX_RETRIES_TAKE_APPOINTMENT
        ):
            self.phone_caller.successful_call = True

        opening_hours = self.company_data.openings
        if not opening_hours:
            opening_hours = json.dumps(self.company_data.openings_2)

        appointment_info = ""

        if not hasattr(self, "appointment_form") or not self.appointment_form:
            self.appointment_form = AppointmentForm(config=self.company_data.config)

        if self.appointment_form and self.appointment_form.start_date:
            appointment_info = f"""
            Informations sur le rendez-vous qui vient d'être pris :
            Votre rendez-vous est fixé le {self.appointment_form.start_date.strftime('%d/%m/%Y')} à {self.appointment_form.start_date.strftime('%H:%M')}.
            Pour le modifier ou l'annuler, merci de faire un nouvel appel.            
            """

        context.add_message(
            {
                "role": "system",
                "content": f"""                                
                A partir de maintenant, l'appel est terminé et 
                Tu peux seulement répondre aux questions suivantes :
               
                Address: {self.company_data.address}
                Heures d'ouverture: {opening_hours}
                Additional information:
                {self.company_data.additional_information}
                
                {appointment_info}
                
                IMPORTANT:
                Tu n'as surtout plus le droit de créer, modifier ou annuler un rendez-vous. Un nouvel appel est nécessaire pour cela.
                Tu ne peux pas répondre à des questions médicales.
                """,
            }
        )

        await self._say_and_wait(
            llm,
            context,
            msg,
        )

    async def handle_last_question(
        self,
        function_name,
        tool_call_id,
        args,
        llm: OpenAILLMService,
        context: OpenAILLMContext,
        result_callback,
    ):
        has_last_question = args.get("has_last_question", False)

        if has_last_question:
            self.phone_caller.has_note = True
            msg = "Pas de problème, quel est le message que vous souhaitez laisser ?"
            if not self._patient_data.is_identify():
                msg = "Pas de problème, merci de préciser votre prénom et nom de famille puis merci de laisser votre message."

            await self._reply_to_user(
                result_callback,
                context,
                llm,
                f"""
                Dit: {msg}
                --
                Une fois que l'utilisateur a dit tout ce qu'il voulait, demande s'il souhaite ajouter quelque chose d'autre.
                Si le patient a tout dit, dit 'Merci pour votre appel. Je vous souhaite une excellente journée !'               
                """,
            )
            return

        await self._say_and_wait(
            llm,
            context,
            "Je vous remercie pour votre appel. Je vous souhaite une excellente journée !",
        )
