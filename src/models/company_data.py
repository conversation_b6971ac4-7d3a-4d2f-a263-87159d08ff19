from pydantic import BaseModel, field_serializer, model_validator
from typing import Optional, List, Dict
from datetime import datetime
import json

from constants import ClientType
from .inbound_config import InboundConfig
from .booking_provider_type import BookingProviderType
from .patient_data import PatientData
from .bot_configuration import BotConfiguration


class CompanyData(BaseModel):
    config: str
    name: str
    address: str
    email: str
    client_type: ClientType
    bot_configuration: BotConfiguration
    additional_information: Optional[str] = None
    created_at: datetime
    openings: Optional[str] = None
    agenda_id: Optional[List[int]] = None
    forward_number: Optional[str] = None
    search_url: Optional[str] = None
    embed_url: Optional[str] = None
    config_name: Optional[str] = None
    booking_url: Optional[str] = None
    inbound_config_file: InboundConfig
    outbound_config_file: Optional[dict] = None
    openings_2: Optional[dict] = None
    client_name: Optional[str] = None
    airtable_booking_url: Optional[str] = None
    task_in_doctolib: Optional[bool] = False
    audio_intro_url: Optional[str] = None
    booking_provider: Optional[BookingProviderType] = BookingProviderType.DOCTOLIB
    all_in_task: Optional[bool] = False
    possible_patients: Optional[List[PatientData]] = []
    has_unique_agenda: Optional[bool] = False
    twilio_inbound_call_phone_number: Optional[str] = None
    current_to_premiere_motive: Optional[Dict[int, int]] = None
    adult_to_child_motive: Optional[Dict[int, List[int]]] = None
    main_motive_per_speciality: Optional[Dict[int, int]] = None
    is_test: Optional[bool] = False

    @field_serializer("created_at")
    def serialize_datetime(self, dt: datetime) -> str:
        return dt.isoformat()

    @model_validator(mode="before")
    def transform_keys(cls, values):
        if values is None:
            return values
        if "created_at" in values and isinstance(values["created_at"], str):
            values["created_at"] = datetime.fromisoformat(values["created_at"])
        if "inbound_config_file" in values and isinstance(
            values["inbound_config_file"], dict
        ):
            values["inbound_config_file"] = InboundConfig(
                **values["inbound_config_file"]
            )

        if "sms_no_agendas" in values:
            values["has_unique_agenda"] = str(values.pop("sms_no_agendas"))

        return values

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            dict: json.dumps,
        }
