from models import CompanyData
from lib.supabase_client import supabase_client


def find_config_by_agenda_id(
    config_name: str, agenda_id: str | int
) -> CompanyData | None:
    """
    Find the config file by agenda ID in the company data.
    """
    company_datas = supabase_client.get_etablissements(config_name=config_name)
    # find in which company_data the agenda_id is present
    for company in company_datas:
        if company.config in ["config145"]:
            continue

        calendars = company.inbound_config_file.calendars
        if any(calendar.agenda_id == int(agenda_id) for calendar in calendars):
            return company
          
    return None
