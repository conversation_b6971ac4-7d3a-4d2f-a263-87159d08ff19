import asyncio
import os
import argparse
import requests
import json
from datetime import datetime, timedelta

from pipecat.services.openai import OpenAILLMContextFrame

from pipecat.processors.frame_processor import FrameDirection

from loguru import logger
from dotenv import load_dotenv

from datetime import datetime
from utils import convert_utc_to_french_text, convert_email_to_spoken_text

from base_intake_processor import BaseIntakeProcessor

from timing_config import timing_config

global configContent

# Load environment variables
load_dotenv(override=True)

from sound_loader import load_sounds

from supabase import create_client

# Load sound files
sounds = load_sounds()

# Initialize Supabase client
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_KEY")

try:
    supabase = create_client(supabase_url, supabase_key)
except Exception as e:
    logger.error(f"Failed to initialize Supabase client: {str(e)}")
    supabase = None

class RevelisIntakeProcessor(BaseIntakeProcessor):
    def __init__(self, context, llm, tts, callId, config, patient_data):
        super().__init__(context, llm, tts, callId, config, patient_data)
        self._appointment_confirmed = False
        self._before_instructions_given = False
        self._api_call_in_progress = False
        self._ask_date_called = False
        self._availability_check_count = 0
        self._irm_check_done = False

    def _initialize_context(self):
        greeting_message = f"Dis quelque chose comme : 'Bienvenue au centre d'imagerie {configContent['name']}."
            
        greeting_message += " Comment puis-je vous aider ?' Une fois que tu arrives à estimer l'intention de l'utilisateur utilise la fonction check_intent. Si le patient répond juste oui, redemande au patient pourquoi il a appelé."
        
        self._context.add_message({
            "role": "system", 
            "content": greeting_message
        })
        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "check_intent",
                "description": "utilise cette fonction lorsque tu arrives à estimer l'intention de l'utilisateur. Si l'utilisateur souhaite parler à un médecin son intention est question.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "intent": {
                            "type": "string",
                            "description": "intention de l'utilisateur",
                            "enum": ["rendez-vous", "question", "urgence", "retard", "annulation"]
                        }
                    },
                    "required": ["intent"]
                },
            },
        }])

    async def check_intent(self, function_name, tool_call_id, args, llm, context, result_callback):
        intent = args["intent"]

        await self.search_patient(function_name, tool_call_id, args, llm, context, result_callback)

    async def forward_call(self,
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback,
            forward_number=None):
        # Extract forward_number from args if it exists, otherwise use the provided forward_number
        forward_number = args.get('forward_number') or forward_number or configContent['forward_number']
        await super().forward_call(function_name, tool_call_id, args, llm, context, result_callback, forward_number=forward_number)
    
    def _check_if_irm_appointment(self):
        """Helper method to check if the current appointment is for an MRI"""
        # Check single appointment
        visit_motive_name = self._appt_data.get('visit_motive_name', '').lower()
        if 'irm' in visit_motive_name:
            return True
            
        # Check double appointment
        visit_motive_name_1 = self._appt_data.get('visit_motive_name_1', '').lower()
        visit_motive_name_2 = self._appt_data.get('visit_motive_name_2', '').lower()
        
        return 'irm' in visit_motive_name_1 or 'irm' in visit_motive_name_2

    def _get_injection_exam_list(self):
        """Generate the list of exams that require injection confirmation from the configuration"""
        try:
            # Get the configuration data using the same logic as in main function
            if "inbound_config_file" in configContent and configContent["inbound_config_file"]:
                # Vérifier si inbound_config_file est déjà un dictionnaire ou une chaîne JSON
                if isinstance(configContent["inbound_config_file"], dict):
                    config_json = configContent["inbound_config_file"]
                else:
                    config_json = json.loads(configContent["inbound_config_file"])
                
                motives = config_json.get("visit-motives-categories", [])
                
                injection_exams = []
                for motive in motives:
                    name = motive.get("name", "")
                    if name and ("avec injection" in name.lower() or "injecté" in name.lower()):
                        # Clean the name by removing injection terms
                        cleaned_name = name
                        cleaned_name = cleaned_name.replace(" avec injection", "")
                        cleaned_name = cleaned_name.replace(" injecté", "")
                        cleaned_name = cleaned_name.replace(" injectée", "")
                        injection_exams.append(f"        - {cleaned_name}")
                
                return "\n".join(injection_exams) if injection_exams else "        - Aucun examen avec injection disponible"
            else:
                return "        - Aucun examen avec injection disponible"
        except Exception as e:
            print(f"DEBUG - Error generating injection exam list: {str(e)}")
            return "        - Scanner CAI\n        - Scanner du rachis cervical\n        - IRM du rachis cervical\n        - IRM du bassin"

    async def search_patient(self, function_name, tool_call_id, args, llm, context, result_callback):
       
        if self._patient_data.get("first_name") is not None:
            if(args["intent"] == "rendez-vous"):
                self._context.set_tools([{
                    "type": "function",
                    "function": {
                        "name": "set_appt",
                        "description": "utilise cette fonction si l'identité est correcte"
                    },
                }, {
                    "type": "function",
                    "function": {
                        "name": "appt_type",
                        "description": "utilise cette fonction si l'utilisateur dit non",
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'new_patient': {
                                    'type': 'boolean',
                                    'description': 'True si lutilisateur a dit non, ce n\'est pas le patient reconnu'
                                }
                            },
                            'required': ['new_patient']
                        }
                    },
                }])
                self._context.add_message({"role": "system", "content": f"Dis : Appelez-vous pour {self._patient_data['first_name']} {self._patient_data['last_name']} ? Si oui utilise la fonction set_appt, si non utilise la fonction set_question."})
                await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

            elif(args["intent"] == "question"):
                self._context.set_tools([{
                    "type": "function",
                    "function": {
                        "name": "set_question",
                        "description": "utilise cette fonction si l'identité est correcte"
                    },
                }, {
                    "type": "function",
                    "function": {
                        "name": "set_question",
                        "description": "utilise cette fonction si l'utilisateur dit non",
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'new_patient': {
                                    'type': 'boolean',
                                    'description': 'True si lutilisateur a dit non'
                                }
                            },
                            'required': ['new_patient']
                        }
                    },
                }])
                self._context.add_message({"role": "system", "content": f"Dis : Appelez-vous pour {self._patient_data['first_name']} {self._patient_data['last_name']} ? Une fois que l'utilisateur a répondu utilise la fonction set_question."})
                await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

            elif(args["intent"] == "urgence"):
                self._context.set_tools([{
                    "type": "function",
                    "function": {
                        "name": "urgent",
                        "description": "utilise cette fonction si l'identité est correcte"
                    },
                }, {
                    "type": "function",
                    "function": {
                        "name": "urgent",
                        "description": "utilise cette fonction si l'utilisateur dit non",
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'new_patient': {
                                    'type': 'boolean',
                                    'description': 'True si lutilisateur a dit non'
                                }
                            },
                            'required': ['new_patient']
                        }
                    },
                }])
                self._context.add_message({"role": "system", "content": f"Dis : Appelez-vous pour {self._patient_data['first_name']} {self._patient_data['last_name']} ? Une fois que l'utilisateur a répondu utilise la fonction urgent."})
                await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

            elif(args["intent"] == "retard"):
                self._context.set_tools([{
                    "type": "function",
                    "function": {
                        "name": "retard",
                        "description": "utilise cette fonction si l'identité est correcte"
                    },
                }, {
                    "type": "function",
                    "function": {
                        "name": "set_question",
                        "description": "utilise cette fonction si l'utilisateur dit non",
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'new_patient': {
                                    'type': 'boolean',
                                    'description': 'True si lutilisateur a dit non'
                                }
                            },
                            'required': ['new_patient']
                        }
                    },
                }])
                self._context.add_message({"role": "system", "content": f"Dis : Appelez-vous pour {self._patient_data['first_name']} {self._patient_data['last_name']} ? Une fois que l'utilisateur a répondu utilise la fonction retard. Si le patient dit non, utilisez la fonction set_question."})
                await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

            elif(args["intent"] == "annulation"):
                self._context.set_tools([{
                    "type": "function",
                    "function": {
                        "name": "appt_intent",
                        "description": "utilise cette fonction si l'identité est correcte",
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'intent': {
                                    'type': 'string',
                                    'description': 'annuler si l`identité est correcte ou l`utilisateur dit oui'
                                }
                            },
                            'required': ['intent']
                        }
                    },
                }, {
                    "type": "function",
                    "function": {
                        "name": "set_question",
                        "description": "utilise cette fonction si l'utilisateur dit non",
                        'parameters': {
                            'type': 'object',
                            'properties': {
                                'new_patient': {
                                    'type': 'boolean',
                                    'description': 'True si lutilisateur a dit non'
                                }
                            },
                            'required': ['new_patient']
                        }
                    },
                }])
                self._context.add_message({"role": "system", "content": f"Dis : tes-vous bien {self._patient_data['first_name']} {self._patient_data['last_name']} ? Une fois que l'utilisateur a répondu utilise la fonction appt_intent. Si le patient dit non, utilisez la fonction set_question."})
                await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

        else:
            if(args["intent"] == "rendez-vous"):
                await self.appt_type(function_name, tool_call_id, {"new_patient": True}, llm, context, result_callback)
            elif(args["intent"] == "urgence"):
                await self.urgent(function_name, tool_call_id, {"new_patient": True}, llm, context, result_callback)
            elif(args["intent"] == "question" or args["intent"] == "annulation"):
                await self.set_question(function_name, tool_call_id, {"new_patient": True}, llm, context, result_callback)
            elif(args["intent"] == "retard"):
                await self.unrecognized_retard(function_name, tool_call_id, {"new_patient": True}, llm, context, result_callback)

    async def unrecognized_retard(self, function_name, tool_call_id, args, llm, context, result_callback):

        try:
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            task_data = {
                "callId": f"{self._callId} - {current_time}",
                "config": self._config,
                "patient_data": self._patient_data,
                "phone_number": self._caller_phone_number,
                "tag": "Rendez-vous",
                "status": False,
                "task": "Le patient a signalé un retard à son rendez-vous au téléphone."
            }
            supabase.table("tasks").insert(task_data).execute()
        except Exception as e:
            logger.error(f"Failed to insert task into Supabase: {str(e)}")

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "forward_call",
                "description": "Utilise cette fonction si le patient a d'autres questions",
            },
        }])

        self._context.add_message({"role": "system", "content": "Dis : 'J'ai bien signalé votre retard au secrétariat, si le retard est trop important, il est possible que le rendez-vous soit annulé. Je peux aussi vous transférer directement au centre pour que vous les préveniez.' Si le patient souhaite être transféré au centre utilise la fonction forward_call "})
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def retard(self, function_name, tool_call_id, args, llm, context, result_callback):
        api_url = "https://doctolib-secret-scrape-global.onrender.com/api/v1/modify-appt"
        request_body = {
            "config": self._config,
            "id": self._patient_data.get("appt_id"),
            "note": "Le patient a signalé un retard au téléphone par Vocca"
        }
        headers = {
            "Authorization": f"Bearer {self._api_token}",
            "Content-Type": "application/json"
        }

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "forward_call",
                "description": "Utilise cette fonction si le patient a d'autres questions",
            },
        }])

        
        asyncio.create_task(asyncio.to_thread(requests.put, api_url, json=request_body, headers=headers))

        self._context.add_message({"role": "system", "content": "Dis : 'J'ai bien signalé votre retard au médecin, si le retard est trop important, il est possible que le rendez-vous soit annulé. Je peux aussi vous transférer directement au centre pour que vous les préveniez.' "})

        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def urgent(self, function_name, tool_call_id, args, llm, context, result_callback):

        self._new_patient = args.get("new_patient", False)

        self._context.set_tools([{
                "type": "function",
                "function": {
                    "name": "appt_type",
                    "description": "utilise cette fonction si l'utilisateur souhaite un rendez-vous d'urgence",
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "set_question",
                    "description": "utilise cette fonction si l'utilisateur souhaite laisser une note d'urgence au centre",
                }
            }])
        
        self._context.add_message({"role": "system", "content": "Est-ce pour un rendez-vous d'urgence ou souhaitez vous laisser une note d'urgence au centre ? Pour une urgence médicale suggère au patient de contacter l'hôpital le plus proche. Si l'utilisateur souhaite laisser une note d'urgence utilise la fonction set_question. Si l'utilisateur souhaite un rendez-vous d'urgence utilisa la fonction appt_type."})
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def set_appt(self, function_name, tool_call_id, args, llm, context, result_callback):

        if self._new_patient:
            await self.appt_type(function_name, tool_call_id, {"new_patient": True}, llm, context, result_callback)
            return
        
        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "appt_intent",
                "description": "utilise cette fonction lorsque tu as compris l'intention de l'utilisateur",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "intent": {
                            "type": "string",
                            "description": "intention de l'utilisateur",
                            "enum": ["nouveau", "modifier", "annuler"]
                        }
                    },
                    "required": ["intent"]
                },
            },
        }])
        self._context.add_message({"role": "system","content": "Parfait. Souhaitez-vous un nouveau rendez-vous annuler ou modifier un rendez-vous ? Une fois que l'utilisateur a répondu utilise la fonction appt_intent."})
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def appt_type(self, function_name, tool_call_id, args, llm, context, result_callback):
        self._new_patient = args.get("new_patient", False)
        self._starting_double_appointment = False

        content = "Merci d'avoir pris contact avec Revelis, vous pouvez prendre rendez-vous sur internet avec le lien Doctolib suivant : " + configContent["booking_url"]

        # Create webhook task but don't await it
        webhook_url = "https://n8n-self-hosted-vocca.onrender.com/webhook/46ace88a-b96e-4900-8dc3-4e5210f69d53"
        payload = {
            "to": "" + self._caller_phone_number,
            "content": content,
            "from": "Revelis"
        }
        
        # Create task but don't await it
        asyncio.create_task(asyncio.to_thread(requests.post, webhook_url, json=payload))

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "appt_unique_type",
                "description": "Utilise cette fonction si le patient souhaite un seul type d'examen"
            }
        },
        {
            "type": "function",
            "function": {
                "name": "appt_double_type",
                "description": "Utilise cette fonction si le patient souhaite deux examens"
            }
        }])
        
        self._context.add_message({"role": "system", "content": "dis : Souhaitez-vous un seul type d'examen ou deux examens ? Si le patient souhaite un double rendez-vous utilise la fonction appt_double_type. Si le patient souhaite un seul rendez-vous utilise la fonction appt_unique_type."})
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def appt_double_type(self, function_name, tool_call_id, args, llm, context, result_callback):

        if self._starting_double_appointment:
            return
        
        self._starting_double_appointment = True
        
        additional_info = args.get("additional_info", None)

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "appt_double_type_1",
                "description": "Utilise cette fonction lorsque tu as compris le premier type de rendez-vous précis souhaité",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "visit_motive_1": {
                            "type": "string",
                            "description": "premier type de rendez-vous précis souhaité",
                        }
                    },
                    "required": ["visit_motive_1"]
                },
            },
        }])

        # Special prompt for config120 to ask about MRI field type for first exam
        if self._config == "config120":
            prompt_content = f"""Dis : '{additional_info} Quel est le premier type d'examen souhaité ?' 
            Si le patient réponds uniquement Échographie, Radiographie, Écho-Doppler, Scanner, IRM, ponctions, demande pour quelle partie du corps le patient souhaite l'examen. 
            Les examens peuvent aussi être des ostéodensitométries, des mammographies, des biopsies, des infiltrations, pas besoin de demander la partie du corps pour ceux-ci. 
                                       
            Pour les IRM, demande d'abord pour quelle partie du corps, puis demande si le patient préfère un IRM à champ ouvert ou à champ fermé et ajoute cette information dans l'argument visit_motive_1 (par exemple "IRM du genou à champ ouvert" ou "IRM du genou à champ fermé").
                                       
            Pour les scanners et IRMs suivants, demande si l'ordonnance indique qu'un produit de contraste par injection est nécessaire puis ajoute dans l'argument visit_motive_1 'avec injection' ou 'sans injection' :
            {self._get_injection_exam_list()}
        
            Pour tous les autres types d'examens, pas besoin de demander si l'ordonnance indique qu'un produit de contraste par injection est nécessaire, le centre réalise tous les autres motifs de scanners, IRMs, ponctions, biopsies, infiltrations. Le motif peut correspondre à tout autre motif de rendez-vous pas seulement ceux cités au-dessus.
                                       
            Tu peux aussi demander de préciser droite ou gauche pour les examens qui le nécessitent.
                                       
            Ne demande pas le deuxième type d'examen à cette étape. Une fois que le patient a répondu, utilise la fonction appt_double_type_1 une seule fois."""
        else:
            prompt_content = f"""Dis : '{additional_info} Quel est le premier type d'examen souhaité ?' 
            Si le patient réponds uniquement Échographie, Radiographie, Écho-Doppler, Scanner, IRM, ponctions, demande pour quelle partie du corps le patient souhaite l'examen. 
            Les examens peuvent aussi être des ostéodensitométries, des mammographies, des biopsies, des infiltrations, pas besoin de demander la partie du corps pour ceux-ci. 
                                       
            Pour les scanners et IRMs suivants, demande si l'ordonnance indique qu'un produit de contraste par injection est nécessaire puis ajoute dans l'argument visit_motive_1 'avec injection' ou 'sans injection' :
            {self._get_injection_exam_list()}
        
            Pour tous les autres types d'examens, pas besoin de demander si l'ordonnance indique qu'un produit de contraste par injection est nécessaire, le centre réalise tous les autres motifs de scanners, IRMs, ponctions, biopsies, infiltrations. Le motif peut correspondre à tout autre motif de rendez-vous pas seulement ceux cités au-dessus.
                                       
            Tu peux aussi demander de préciser droite ou gauche pour les examens qui le nécessitent.
                                       
            Ne demande pas le deuxième type d'examen à cette étape. Une fois que le patient a répondu, utilise la fonction appt_double_type_1 une seule fois."""

        self._context.add_message({"role": "system", "content": prompt_content})
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

        self._double_type_in_progress = False

    
    async def appt_double_type_1(self, function_name, tool_call_id, args, llm, context, result_callback):
        self._appt_data["visit_motive_1"] = args["visit_motive_1"]

        self._checking_double_appointment = False

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "appt_check_double_type",
                "description": "Utilise cette fonction lorsque tu as compris le second type de rendez-vous précis souhaité",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "visit_motive_2": {
                            "type": "string",
                            "description": "second type de rendez-vous précis souhaité",
                        }
                    },
                    "required": ["visit_motive_2"]
                },
            },
        },
        {
            "type": "function",
            "function": {
                "name": "set_question",
                "description": "Utilise cette fonction si le patient a d'autres questions",
            },
        }])

        # Special prompt for config120 to ask about MRI field type for second exam
        if self._config == "config120":
            prompt_content = f"""Dis : 'Quel est le second type d'examen souhaité ?' 
            Si le patient réponds uniquement Échographie, Radiographie, Écho-Doppler, Scanner, IRM, ponctions, demande pour quelle partie du corps le patient souhaite l'examen. 
            Les examens peuvent aussi être des ostéodensitométries, des mammographies, des biopsies, des infiltrations. 
                                       
            Pour les IRM, demande d'abord pour quelle partie du corps, puis demande si le patient préfère un IRM à champ ouvert ou à champ fermé et ajoute cette information dans l'argument visit_motive_2 (par exemple "IRM du genou à champ ouvert" ou "IRM du genou à champ fermé").
                                       
            Pour tous les scanners et les IRMs demande si l'ordonnance indique qu'un produit de contraste par injection est nécessaire. Si oui, explique au patient que ce type d'examen nécessaire une attention particulière en raison de l'injection du produit de constraste et que le patient sera recontacté par le secrétariat pour organiser ce rendez-vous spécifique puis utilise la fonction set_question si le patient a d'autres questions. N'utilise surtout pas la fonction appt_check_type dans ce cas.
                                       
            Pour tous les autres types d'examens, pas besoin de demander si l'ordonnance indique qu'un produit de contraste par injection est nécessaire. Le motif peut correspondre à tout autre motif de rendez-vous pas seulement ceux cités au-dessus.
                                       
            Tu peux aussi demander de préciser droite ou gauche pour les examens qui le nécessitent.
                                       
            Une fois que le patient a répondu, utilise la fonction appt_check_double_type."""
        else:
            prompt_content = f"""Dis : 'Quel est le second type d'examen souhaité ?' 
            Si le patient réponds uniquement Échographie, Radiographie, Écho-Doppler, Scanner, IRM, ponctions, demande pour quelle partie du corps le patient souhaite l'examen. 
            Les examens peuvent aussi être des ostéodensitométries, des mammographies, des biopsies, des infiltrations. 
                                       
            Pour les scanners et IRMs suivants, demande si l'ordonnance indique qu'un produit de contraste par injection est nécessaire puis ajoute dans l'argument visit_motive_2 'avec injection' ou 'sans injection' :
            {self._get_injection_exam_list()}
                                       
            Pour tous les autres types d'examens, pas besoin de demander si l'ordonnance indique qu'un produit de contraste par injection est nécessaire. Le motif peut correspondre à tout autre motif de rendez-vous pas seulement ceux cités au-dessus.
                                       
            Tu peux aussi demander de préciser droite ou gauche pour les examens qui le nécessitent.
                                       
            Une fois que le patient a répondu, utilise la fonction appt_check_double_type."""

        self._context.add_message({"role": "system", "content": prompt_content})
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def appt_unique_type(self, function_name, tool_call_id, args, llm, context, result_callback):

        additional_info = args.get("additional_info", None)

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "appt_check_type",
                "description": "Utilise cette fonction lorsque tu as compris le type de rendez-vous précis souhaité par le patient",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "visit_motive": {
                            "type": "string",
                            "description": "type de rendez-vous précis souhaité",
                        }
                    },
                    "required": ["visit_motive"]
                },
            },
        }, {
            "type": "function",
            "function": {
                "name": "set_question",
                "description": "Utilise cette fonction si le patient a d'autres questions",
            },
        }])

        # Special prompt for config120 to ask about MRI field type
        if self._config == "config120":
            prompt_content = f"""Dis : '{additional_info} Quel type d'examen souhaitez-vous ?'
            Si le patient réponds uniquement Échographie, Radiographie, Écho-Doppler, Scanner, IRM, ponctions, demande pour quelle partie du corps le patient souhaite l'examen. 
            Les examens peuvent aussi être des ostéodensitométries, des mammographies, des biopsies, des infiltrations. 
                                       
            Pour les IRM, demande d'abord pour quelle partie du corps, puis demande si le patient préfère un IRM à champ ouvert ou à champ fermé et ajoute cette information dans l'argument visit_motive (par exemple "IRM du genou à champ ouvert" ou "IRM du genou à champ fermé").
                                       
            Pour tous les scanners et les IRMs demande si l'ordonnance indique qu'un produit de contraste par injection est nécessaire. Si oui, explique au patient que ce type d'examen nécessaire une attention particulière en raison de l'injection du produit de constraste et que le patient sera recontacté par le secrétariat pour organiser ce rendez-vous spécifique puis utilise la fonction set_question si le patient a d'autres questions. N'utilise surtout pas la fonction appt_check_type dans ce cas.

            Pour tous les autres types d'examens, pas besoin de demander si l'ordonnance indique qu'un produit de contraste par injection est nécessaire. Le motif peut correspondre à tout autre motif de rendez-vous pas seulement ceux cités au-dessus.
                                       
            Tu peux aussi demander de préciser droite ou gauche pour les examens qui le nécessitent.
                                                                                                                                                                                                                                                                
            Une fois que le patient a répondu, utilise la fonction appt_check_type."""
        else:
            prompt_content = f"""Dis : '{additional_info} Quel type d'examen souhaitez-vous ?'
            Si le patient réponds uniquement Échographie, Radiographie, Écho-Doppler, Scanner, IRM, ponctions, demande pour quelle partie du corps le patient souhaite l'examen. 
            Les examens peuvent aussi être des ostéodensitométries, des mammographies, des biopsies, des infiltrations. 
                                       
            Pour les scanners et IRMs suivants, demande si l'ordonnance indique qu'un produit de contraste par injection est nécessaire puis ajoute dans l'argument visit_motive 'avec injection' ou 'sans injection' :
            {self._get_injection_exam_list()}

            Pour tous les autres types d'examens, pas besoin de demander si l'ordonnance indique qu'un produit de contraste par injection est nécessaire. Le motif peut correspondre à tout autre motif de rendez-vous pas seulement ceux cités au-dessus.
                                       
            Tu peux aussi demander de préciser droite ou gauche pour les examens qui le nécessitent.
                                                                                                                                                                                                                                                                
            Une fois que le patient a répondu, utilise la fonction appt_check_type."""

        self._context.add_message({"role": "system", "content": prompt_content})
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def appt_check_double_type(self, function_name, tool_call_id, args, llm, context, result_callback):

        if self._checking_double_appointment:
            return

        self._appt_data["visit_motive_2"] = args["visit_motive_2"]

        print(self._appt_data["visit_motive_1"])
        print(self._appt_data["visit_motive_2"])

        # Check if we need to reorder: put radiography before ultrasound
        motive1_lower = self._appt_data["visit_motive_1"].lower()
        motive2_lower = self._appt_data["visit_motive_2"].lower()
        
        # If motive2 is radiography and motive1 is ultrasound, swap them
        if (any(term in motive2_lower for term in ["radio", "radiographie"]) and 
            any(term in motive1_lower for term in ["echo", "échographie"])):
            # Swap the motives
            self._appt_data["visit_motive_1"], self._appt_data["visit_motive_2"] = self._appt_data["visit_motive_2"], self._appt_data["visit_motive_1"]
            print("Motifs réordonnés: Radio avant Écho")
            print(self._appt_data["visit_motive_1"])
            print(self._appt_data["visit_motive_2"])

        # Check for mammography in config73, config121, config122, but allow mammography + mammary ultrasound combination
        if self._config in ["config73", "config121", "config122"] and (any(term in motive1_lower for term in ["mammographie", "mammo"]) or 
                                          any(term in motive2_lower for term in ["mammographie", "mammo"])):
            
            # Check if it's the specific combination of mammography + mammary ultrasound
            has_mammography = any(term in motive1_lower for term in ["mammographie", "mammo"]) or any(term in motive2_lower for term in ["mammographie", "mammo"])
            has_mammary_echo = any(term in motive1_lower for term in ["échographie mammaire", "echographie mammaire", "écho mammaire", "echo mammaire"]) or any(term in motive2_lower for term in ["échographie mammaire", "echographie mammaire", "écho mammaire", "echo mammaire"])
            
            # If it's mammography + mammary ultrasound for config73, config121, or config122, keep as double appointment
            if has_mammography and has_mammary_echo and self._config in ["config73", "config121", "config122"]:
                print(f"Combinaison mammographie + échographie mammaire détectée pour {self._config} - garde en double rendez-vous")
                # Set the specific IDs
                self._appt_data['visit_motive_id_1'] = 10771262
                self._appt_data['visit_motive_id_2'] = 10768963
                self._appt_data['visit_motive_name_1'] = "Mammographie"
                self._appt_data['visit_motive_name_2'] = "Échographie mammaire"
                self._appt_data['visit_motive_name'] = "Mammographie + Échographie mammaire"
                
                # Different pricing and instructions based on config
                if self._config == "config73":
                    self._appt_data['after'] = "Le montant total de l'examen à avancer est de 200 euros, vous serez remboursés par la sécurité sociale sur une base de 87 euros. Veuillez apporter vos anciens examens."
                elif self._config == "config121":
                    self._appt_data['after'] = "Le montant total de l'examen à avancer est de 250 euros, vous serez remboursés par la sécurité sociale sur une base de 87 euros."
                elif self._config == "config122":
                    self._appt_data['after'] = ""
                
                self._appt_data['before'] = ""
                self._appt_data['open'] = True
                
                self._context.set_tools([{
                    "type": "function",
                    "function": {
                        "name": "ask_date",
                        "description": "Utilise cette fonction si les types de rendez-vous sont corrects",
                    }}, {
                        "type": "function",
                        "function": {
                            "name": "appt_double_type",
                            "description": "Utilise cette fonction si les types de rendez-vous sont incorrects",
                        },
                    }])
                
                self._context.add_message({"role": "system", "content": f"Demande au patient s'il souhaite bien prendre rendez-vous pour une mammographie. Si c'est correct, utilisez la fonction ask_date. Sinon, utilisez la fonction appt_double_type."})
                await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
                return
            elif has_mammography and has_mammary_echo and self._config == "config122":
                # For config122, also keep as double appointment like config73 and config121
                print("Combinaison mammographie + échographie mammaire détectée pour config122 - garde en double rendez-vous")
                # Set the specific IDs
                self._appt_data['visit_motive_id_1'] = 10771262
                self._appt_data['visit_motive_id_2'] = 10768963
                self._appt_data['visit_motive_name_1'] = "Mammographie"
                self._appt_data['visit_motive_name_2'] = "Échographie mammaire"
                self._appt_data['visit_motive_name'] = "Mammographie + Échographie mammaire"
                self._appt_data['after'] = ""
                self._appt_data['before'] = ""
                self._appt_data['open'] = True
                
                self._context.set_tools([{
                    "type": "function",
                    "function": {
                        "name": "ask_date",
                        "description": "Utilise cette fonction si les types de rendez-vous sont corrects",
                    }}, {
                        "type": "function",
                        "function": {
                            "name": "appt_double_type",
                            "description": "Utilise cette fonction si les types de rendez-vous sont incorrects",
                        },
                    }])
                
                self._context.add_message({"role": "system", "content": f"Demande au patient s'il souhaite bien prendre rendez-vous pour une mammographie. Si c'est correct, utilisez la fonction ask_date. Sinon, utilisez la fonction appt_double_type."})
                await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
                return
            else:
                # For other mammography combinations, stop the flow
                self._context.set_tools([{
                    "type": "function",
                    "function": {
                        "name": "set_question",
                        "description": "Utilise cette fonction si le patient a d'autres questions",
                    },
                }])
                
                self._context.add_message({"role": "system", "content": "Je suis désolé, nous avons besoin d'organiser spécialement les doubles rendez-vous incluant une mammographie. J'ai pris note de votre demande pour ces deux examens et le secrétariat vous recontactera très rapidement pour organiser ces rendez-vous. Avez-vous d'autres questions ?"})
                await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
                return

        if any(exam in self._appt_data["visit_motive_1"].lower() for exam in ["scanner"]) and self._config in ["config73", "config121", "config122"]:
            
            await self.scanner(function_name, tool_call_id, args, llm, context, result_callback)
            return

        elif any(exam in self._appt_data["visit_motive_1"].lower() for exam in ["infiltration", "radio", "radiographie"]) and self._config in ["config73", "config121"]:
            
            await self.scanner(function_name, tool_call_id, args, llm, context, result_callback)
            return

        elif any(exam in self._appt_data["visit_motive_1"].lower() for exam in ["irm"]) and self._config in ["config73", "config121", "config122", "config123"]:

            await self.irm(function_name, tool_call_id, args, llm, context, result_callback)
            return

        elif any(exam in self._appt_data["visit_motive_1"].lower() for exam in ["hystérosalpingographie", "hysterosalpingographie", "hystéro"]) and self._config == "config121":
            
            await self.hysterosalpingographie(function_name, tool_call_id, args, llm, context, result_callback)
            return
            
        elif any(exam in self._appt_data["visit_motive_2"].lower() for exam in ["scanner"]) and self._config in ["config73", "config121", "config122"]:
            
            await self.scanner(function_name, tool_call_id, args, llm, context, result_callback)
            return

        elif any(exam in self._appt_data["visit_motive_2"].lower() for exam in ["infiltration", "radio", "radiographie"]) and self._config in ["config73", "config121"]:
            
            await self.scanner(function_name, tool_call_id, args, llm, context, result_callback)
            return


        elif any(exam in self._appt_data["visit_motive_2"].lower() for exam in ["irm"]) and self._config in ["config73", "config121", "config122", "config123"]:

            await self.irm(function_name, tool_call_id, args, llm, context, result_callback)
            return

        elif any(exam in self._appt_data["visit_motive_2"].lower() for exam in ["hystérosalpingographie", "hysterosalpingographie", "hystéro"]) and self._config == "config121":
            
            await self.hysterosalpingographie(function_name, tool_call_id, args, llm, context, result_callback)
            return

        elif (any(exam in self._appt_data["visit_motive_1"].lower() for exam in ["scanner", "irm"]) or any(exam in self._appt_data["visit_motive_2"].lower() for exam in ["scanner", "irm"])) and self._config == "config32":
            
            self._context.add_message({"role": "system", "content": "Je suis désolé, mais je ne peux pas prendre ces deux rendez-vous au centre de Ourcq. Je vais donc laisser une note au secrétariat pour qu'ils puissent puisse vous recontacter et organiser vos rendez-vous sur des jours différents. Tu ne peux pas vérifier les disponibilités pour les deux rendez-vous ni prendre de rendez-vous."})
            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
            return
        
        self._checking_double_appointment = True
        
        self._api_call_in_progress = True

        async def make_api_call(visit_motive):
            api_url = "https://doctolib-secret-scrape-global.onrender.com/api/v1/get-visit-motive-id"
            request_body = {
                "config": self._config,
                "practice": visit_motive
            }
            headers = {
                "Authorization": f"Bearer {self._api_token}",
                "Content-Type": "application/json"
            }
            response = await asyncio.to_thread(requests.post, api_url, json=request_body, headers=headers)
            return response.json()
        
        # Run both API calls and TTS concurrently
        api_result_1, api_result_2, _ = await asyncio.gather(
            make_api_call(self._appt_data["visit_motive_1"]),
            make_api_call(self._appt_data["visit_motive_2"]),
            self._tts.say("Je consulte nos motifs de rendez-vous.")
        )

        self._api_call_in_progress = False

        # Process the results of both API calls
        data_1 = api_result_1
        visit_motive_id_1 = data_1.get("visit_motive_id")
        data_2 = api_result_2
        visit_motive_id_2 = data_2.get("visit_motive_id")

        # Check if visit motive IDs are the same
        if visit_motive_id_1 == visit_motive_id_2:
            self._starting_double_appointment = False
            self._checking_double_appointment = False
            await self.appt_double_type(function_name, tool_call_id, {
                "additional_info": "Désolé mais je n'ai pas trouvé vos deux motifs d'examen. Reprenons depuis le début."
            }, llm, context, result_callback)
            return

        price_1 = data_1.get("price", "")
        price_2 = data_2.get("price", "")
        instructions_1 = data_1.get("instructions", "")
        instructions_2 = data_2.get("instructions", "")
        open_1 = data_1.get("open", "")
        open_2 = data_2.get("open", "")

        price_message_1 = f" Pour cet examen, le dépassement d'honoraires est de {price_1} euros, le centre pratique le tiers payant mais n'avance pas les frais de mutuelle." if price_1 else ""
        price_message_2 = f" Pour cet examen, le dépassement d'honoraires est de {price_2} euros, le centre pratique le tiers payant mais n'avance pas les frais de mutuelle." if price_2 else ""

        if(open_1 == False or open_2 == False):
            await self.set_question(function_name, tool_call_id, {"new_patient": self._new_patient}, llm, context, result_callback)
            return
        
        if "IRM" in self._appt_data["visit_motive_1"].upper() and "IRM" in self._appt_data["visit_motive_2"].upper():
            self._context.add_message({"role": "system", "content": "Je suis désolé, il n'est pas possible de prendre deux rendez-vous d'IRM dans la même journée. Je vais laisser une note au secrétariat pour qu'ils puissent puisse vous recontacter et organiser vos rendez-vous sur des jours différents. Tu ne peux pas vérifier les disponibilités pour les deux rendez-vous ni prendre de rendez-vous."})
            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
            return
        elif any(term in self._appt_data["visit_motive_1"].lower() for term in ["échographie", "echographie"]) and any(term in self._appt_data["visit_motive_2"].lower() for term in ["échographie", "echographie"]) and self._config not in ["config122", "config123", "config120"]:
            self._context.add_message({"role": "system", "content": "Je suis désolé, il n'est pas possible de prendre deux rendez-vous d'échographie dans la même journée. Je vais laisser une note au secrétariat pour qu'ils puissent puisse vous recontacter et organiser vos rendez-vous sur des jours différents. Tu ne peux pas vérifier les disponibilités pour les deux rendez-vous ni prendre de rendez-vous."})
            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
            return

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "ask_date",
                "description": "Utilise cette fonction si les types de rendez-vous sont corrects",
            }}, {
                "type": "function",
                "function": {
                    "name": "appt_double_type",
                    "description": "Utilise cette fonction si les types de rendez-vous sont incorrects",
                },
            }])

        if visit_motive_id_1 != "error" and visit_motive_id_1 is not None and visit_motive_id_2 != "error" and visit_motive_id_2 is not None:
            self._appt_data['visit_motive_name_1'] = data_1.get("visit_motive_name")
            self._appt_data['visit_motive_name_2'] = data_2.get("visit_motive_name")
            self._appt_data['visit_motive_id_1'] = visit_motive_id_1
            self._appt_data['visit_motive_id_2'] = visit_motive_id_2
            self._appt_data['visit_motive_name'] = f"{data_1['visit_motive_name']} + {data_2['visit_motive_name']}"
            self._appt_data['after'] = "Pour le premier rendez-vous " + instructions_1 + " et pour le second rendez-vous " + instructions_2

            if (price_message_1 or price_message_2) and self._config not in ["config73", "config121", "config122"]:
                self._appt_data['before'] = "Pour le premier rendez-vous " + price_message_1 + " et pour le second rendez-vous " + price_message_2 + " Ces prix peuvent être amenés à évoluer."
            else:
                self._appt_data['before'] = ""

            self._context.add_message({"role": "system", "content": f"Demande au patient s'il souhaite bien prendre rendez-vous pour {data_1['visit_motive_name'].lower()} et {data_2['visit_motive_name'].lower()}. Demande bien pour ce motif : {data_1['visit_motive_name'].lower()} et {data_2['visit_motive_name'].lower()} pas un autre. Si c'est correct, utilisez la fonction ask_date. Sinon, utilisez la fonction appt_double_type."})

            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
        else:

            print("Motifs d'examen non trouvés")
            self._starting_double_appointment = False
            await self.appt_double_type(function_name, tool_call_id, {"additional_info": "Je n'ai pas trouvé vos motifs d'examens."}, llm, context, result_callback)

    
    async def appt_check_type(self, function_name, tool_call_id, args, llm, context, result_callback):

        if any(exam in args["visit_motive"].lower() for exam in ["scanner"]) and self._config in ["config73", "config121", "config122"]:
            
            await self.scanner(function_name, tool_call_id, args, llm, context, result_callback)
            return

        elif any(exam in args["visit_motive"].lower() for exam in ["infiltration", "radio", "radiographie"]) and self._config in ["config73", "config121"]:
            
            await self.scanner(function_name, tool_call_id, args, llm, context, result_callback)
            return

        elif any(exam in args["visit_motive"].lower() for exam in ["irm", "i.r.m", "i r m"]) and self._config in ["config73", "config121", "config122", "config123"]:

            await self.irm(function_name, tool_call_id, args, llm, context, result_callback)
            return

        elif any(exam in args["visit_motive"].lower() for exam in ["hystérosalpingographie", "hysterosalpingographie", "hystéro"]) and self._config == "config121":
            
            await self.hysterosalpingographie(function_name, tool_call_id, args, llm, context, result_callback)
            return

        # Special case for config73, config121, and config122: if mammography is requested as single exam, convert to double exam
        elif any(exam in args["visit_motive"].lower() for exam in ["mammographie", "mammo"]) and self._config in ["config73", "config121", "config122"]:
            # Convert to double appointment with mammography + echo mammaire
            self._appt_data["visit_motive_1"] = args["visit_motive"]
            self._appt_data["visit_motive_2"] = "Échographie mammaire"
            self._appt_data['visit_motive_id_1'] = 10771262
            self._appt_data['visit_motive_id_2'] = 10768963
            self._appt_data['visit_motive_name_1'] = "Mammographie"
            self._appt_data['visit_motive_name_2'] = "Échographie mammaire"
            self._appt_data['visit_motive_name'] = "Mammographie + Échographie mammaire"
            
            # Different pricing and instructions based on config
            if self._config == "config73":
                self._appt_data['after'] = "Le montant total de l'examen à avancer est de 200 euros, vous serez remboursés par la sécurité sociale sur une base de 87 euros. Veuillez apporter vos anciens examens."
                self._appt_data['price'] = 200
            elif self._config == "config121":
                self._appt_data['after'] = "Le montant total de l'examen à avancer est de 250 euros, vous serez remboursés par la sécurité sociale sur une base de 87 euros."
                self._appt_data['price'] = 150
            elif self._config == "config122":
                self._appt_data['after'] = ""
                self._appt_data['price'] = 0
            
            self._appt_data['before'] = ""
            self._appt_data['open'] = True
            
            self._context.set_tools([{
                "type": "function",
                "function": {
                    "name": "ask_date",
                    "description": "Utilise cette fonction si le type de rendez-vous est correct",
                }}, {
                    "type": "function",
                    "function": {
                        "name": "appt_unique_type",
                        "description": "Utilise cette fonction si le type de rendez-vous est incorrect",
                    },
                }])
            
            self._context.add_message({"role": "system", "content": f"Demande au patient s'il souhaite bien prendre rendez-vous pour une mammographie. Si c'est le bon type de rendez-vous utilise la fonction ask_date. Sinon utilise la fonction appt_unique_type."})
            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
            return

        self._api_call_in_progress = True

        async def make_api_call():
            api_url = "https://doctolib-secret-scrape-global.onrender.com/api/v1/get-visit-motive-id"
            request_body = {
                "config": self._config,
                "practice": args["visit_motive"]
            }
            headers = {
                "Authorization": f"Bearer {self._api_token}",
                "Content-Type": "application/json"
            }
            response = await asyncio.to_thread(requests.post, api_url, json=request_body, headers=headers)
            return response.json()

        # Run API call and TTS concurrently
        api_result, _ = await asyncio.gather(
            make_api_call(),
            self._tts.say("Je consulte nos motifs de rendez-vous.")
        )

        self._api_call_in_progress = False

        data = api_result
        visit_motive_id = data.get("visit_motive_id")
        instructions = data.get("instructions", "")
        price = data.get("price", "")
        open = data.get("open", "")

        if price != "" and self._config not in ["config73", "config121", "config122", "config120", "config123"]:
            price = " Pour cet examen, le dépassement d'honoraires est de " + price + " euros, le centre pratique le tiers payant mais n'avance pas les frais de mutuelle."

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "ask_date",
                "description": "Utilise cette fonction si le type de rendez-vous est correct",
            }}, {
                "type": "function",
                "function": {
                    "name": "appt_unique_type",
                    "description": "Utilise cette fonction si le type de rendez-vous est incorrect",
                },
            }])
        
        if(open == False):
            await self.set_question(function_name, tool_call_id, {"new_patient": self._new_patient}, llm, context, result_callback)
            return

        if visit_motive_id != "error" and visit_motive_id is not None:
            self._appt_data['visit_motive_id'] = visit_motive_id
            self._appt_data['visit_motive_name'] = data.get("visit_motive_name")
            self._appt_data['before'] = price
            self._appt_data['after'] = instructions
            self._context.add_message({"role": "system", "content": f"Demande au patient s'il souhaite bien prendre rendez-vous pour {data['visit_motive_name'].lower()}. Si c'est le bon type de rendez-vous utilise la fonction ask_date. Sinon utilise la fonction appt_unique_type."})
            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
        else:
            await self.appt_unique_type(function_name, tool_call_id, {"additional_info": "Je n'ai pas trouvé votre type d'examen."}, llm, context, result_callback)

    
    async def ask_date(self, function_name, tool_call_id, args, llm, context, result_callback):
        self._availability_checked = False

        self._appt_data["agenda_id"] = configContent["agenda_id"]

        # Set flags based on args
        skip_irm_check = args.get("skip_irm_check", False)
        
        # Update instance flags if provided in args
        if skip_irm_check:
            self._irm_check_done = True
            
        # Check if this is an IRM (MRI) appointment and check not done yet for config120
        if not self._irm_check_done and self._check_if_irm_appointment() and self._config == "config120":
            await self.irm_pacemaker_check(function_name, tool_call_id, args, llm, context, result_callback)
            return

        if self._ask_date_called:
            message = "D'accord, à quelle date et heure préférez-vous votre rendez-vous ?"
        else:
            message = "Souhaitez-vous votre rendez-vous le plus tôt possible ou à une date précise ?"

        self._ask_date_called = True

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "check_availability",
                "description": "Utilise cette fonction pour vérifier les disponibilités à la date et l'heure spécifiées. Si le patient souhaite le plus tôt possible, inscris la date du lendemain.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "time": {
                            "type": "string",
                            "description": "La date et l'heure souhaités pour le ou les rendez-vous",
                            "format": "date-time",
                            "example": "2024-08-31T14:55:01.123456+00:00"
                        },    
                    },
                    "required": ["time"]
                },
            },
        }])

        self._appt_data["additional_warning"] = ""

        # Safely get visit_motive_name with a default empty string
        visit_motive_name = self._appt_data.get("visit_motive_name", "").lower()
        if any(exam in visit_motive_name for exam in ["irm", "scanner"]) and self._config == "config32":
            self._appt_data["additional_warning"] = "Attention, les IRM et les Scanners sont réalisés au centre d'imagerie médicale de Secrétan au 44 Rue Bouret métro Jaurès et non à Ourcq."
            configContent["address"] = "Centre d'imagerie Révélis Secrétan, 44 Rue Bouret, 75019 Paris"

        before_message = f"{self._appt_data['additional_warning']} {self._appt_data.get('before', '')} " if not self._before_instructions_given else ""

        if self._config in ["config73", "config121", "config122"]:
            before_message += self._appt_data.get('after', '')

        self._before_instructions_given = True 

        self._context.add_message({"role": "system", "content": f"{before_message} {message} Le patient peut souhaiter le plus tôt possible. Une fois que le patient a donné une date et une heure, utilisez la fonction check_availability pour vérifier les disponibilités."})
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)


    async def scanner(self, function_name, tool_call_id, args, llm, context, result_callback):

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "forward_call",
                "description": "Utilise cette fonction pour transférer l'appel vers l'autre centre",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "forward_number": {
                            "type": "string",
                            "description": "Le numéro de téléphone vers lequel transférer l'appel"
                        }
                    },
                    "required": ["forward_number"]
                }
            }
        }])

        # Configure messages and phone numbers based on config
        if self._config == "config73":
            message = "Explique : 'Les scanners et les infiltrations sont réalisés au centre d'imagerie médicale Monceau au 21 Rue de Chazelles et non à Pyramides. Souhaitez-vous que je vous transfère à leur secrétariat ?' Si le patient dit oui, utilise la fonction forward_call avec le numéro +33148882600"
        elif self._config == "config121":
            message = "Explique : 'Les scanners, les infiltrations et les radiographies sont réalisés au centre d'imagerie médicale Monceau au 21 Rue de Chazelles et non à Goumot. Souhaitez-vous que je vous transfère à leur secrétariat ?' Si le patient dit oui, utilise la fonction forward_call avec le numéro +33148882600"
        elif self._config == "config122":
            # TODO: Fill in the specific message and phone number for config122
            message = "Explique : 'Les scanners et les infiltrations sont réalisés au centre d'imagerie médicale de Clignancourt au 108 rue du Ruisseau et non à Saint-Ouen. Souhaitez-vous que je vous transfère à leur secrétariat ?' Si le patient dit oui, utilise la fonction forward_call avec le numéro +33142582828"
        else:
            # Default fallback
            message = "Explique : 'Les scanners et les infiltrations sont réalisés dans un autre centre. Souhaitez-vous que je vous transfère à leur secrétariat ?' Si le patient dit oui, utilise la fonction forward_call avec le numéro approprié"

        self._context.add_message({"role": "system", "content": message})
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    async def irm(self, function_name, tool_call_id, args, llm, context, result_callback):

        # For config73 and config121, create a task instead of transferring the call
        if self._config in ["config73", "config121"]:

            self._context.set_tools([{
                "type": "function",
                "function": {
                    "name": "set_question",
                    "description": "Utilise cette fonction si le patient a d'autres questions",
                },
            }])

         
            message = "J'ai bien pris note de votre demande d'IRM. Ces examens sont réalisés dans un autre centre médical. Le secrétariat vous recontactera dans les meilleurs délais pour organiser votre rendez-vous. Avez-vous d'autres questions ?"
            
            self._context.add_message({"role": "system", "content": message})
            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
            return

        # For other configs, keep the original behavior
        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "forward_call",
                "description": "Utilise cette fonction pour transférer l'appel vers l'autre centre",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "forward_number": {
                            "type": "string",
                            "description": "Le numéro de téléphone vers lequel transférer l'appel"
                        }
                    },
                    "required": ["forward_number"]
                }
            }
        }])

        # Configure messages and phone numbers based on config
        if self._config == "config122":
            # TODO: Fill in the specific message and phone number for config122
            message = "Explique : 'Les IRM sont réalisés au centre d'imagerie médicale de Clignancourt au 108 rue du Ruisseau et non à Saint-Ouen. Souhaitez-vous que je vous transfère à leur secrétariat ?' Si le patient dit oui, utilise la fonction forward_call avec le numéro +33142582828)"
        elif self._config == "config123":
            message = "Explique : 'Les IRM sont réalisés au centre Révélis Ourcq. Souhaitez-vous que je vous transfère à leur secrétariat ?' Si le patient dit oui, utilise la fonction forward_call avec le numéro +33140341470"
        else:
            # Default fallback
            message = "Explique : 'Les IRM sont réalisés dans un autre centre. Souhaitez-vous que je vous transfère à leur secrétariat ?' Si le patient dit oui, utilise la fonction forward_call avec le numéro approprié"

        self._context.add_message({"role": "system", "content": message})
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    async def hysterosalpingographie(self, function_name, tool_call_id, args, llm, context, result_callback):

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "forward_call",
                "description": "Utilise cette fonction pour transférer l'appel vers l'autre centre",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "forward_number": {
                            "type": "string",
                            "description": "Le numéro de téléphone vers lequel transférer l'appel"
                        }
                    },
                    "required": ["forward_number"]
                }
            }
        }])

        # Configure messages and phone numbers based on config
        if self._config == "config121":
            message = "Explique : 'Les hystérosalpingographies sont réalisées au centre d'imagerie médicale Pyramides au 13 avenue de l'Opéra et non à Goumot. Souhaitez-vous que je vous transfère à leur secrétariat ?' Si le patient dit oui, utilise la fonction forward_call avec le numéro +33142606497"
        else:
            # Default fallback for other configs
            message = "Explique : 'Les hystérosalpingographies sont réalisées dans un autre centre. Souhaitez-vous que je vous transfère à leur secrétariat ?' Si le patient dit oui, utilise la fonction forward_call avec le numéro approprié"

        self._context.add_message({"role": "system", "content": message})
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    async def irm_pacemaker_check(self, function_name, tool_call_id, args, llm, context, result_callback):
        """Function to handle MRI-specific safety questions for pacemaker"""
        
        # Mark this check as done to avoid infinite loop
        self._irm_check_done = True
        
        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "ask_date",
                "description": "Utilise cette fonction si le patient n'a pas de contre-indications pour l'IRM",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "skip_irm_check": {
                            "type": "boolean",
                            "description": "Flag to skip IRM check to avoid infinite loop"
                        }
                    },
                    "required": ["skip_irm_check"]
                }
            }}, {
            "type": "function",
            "function": {
                "name": "set_question",
                "description": "Utilise cette fonction si le patient a des contre-indications pour l'IRM",
            },
        }])
        
        self._context.add_message({"role": "system", "content": "Demande au patient : 'Avez-vous un objet métallique dans le corps ? un pacemaker ou une valve cardiaque ?' Si le patient répond non (signifiant qu'il n'a pas de contre-indications), utilisez la fonction ask_date avec skip_irm_check=true. Si le patient répond oui (signifiant qu'il a des contre-indications), ou si le patient semble incertain, utilisez la fonction set_question."})
            
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def check_availability(self, function_name, tool_call_id, args, llm, context, result_callback):

        if self._availability_checked == True:
            return

        self._availability_checked = True
        self._availability_check_count += 1
        
        # After 4 attempts, take note for secretariat instead of continuing
        if self._availability_check_count >= 3:
            self._context.set_tools([{
                "type": "function",
                "function": {
                    "name": "set_question",
                    "description": "Utilise cette fonction si le patient a d'autres questions",
                },
            }])

            self._context.add_message({
                "role": "system", 
                "content": "Je vois qu'aucun créneau ne semble vous convenir. Je vais prendre note de votre demande de rendez-vous et transmettre votre préférence de date au secrétariat. Ils vous recontacteront dans les meilleurs délais pour vous proposer des créneaux qui correspondent mieux à vos disponibilités. Avez-vous d'autres questions ?"
            })
            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
            return
        
        current_date = datetime.now().strftime("%d %B %Y")

        # Check if this is a double appointment by checking if visit_motive_id_1 exists in appt_data
        is_double_appointment = 'visit_motive_id_1' in self._appt_data

        self._api_call_in_progress = True

        if is_double_appointment:
            api_url = "https://hook.eu1.make.com/q8b6iygl0d0v8jsosj2ylng7vi3nsnuq"
            request_body = {
                "config": self._config,
                "visit_motive_id_1": self._appt_data['visit_motive_id_1'],
                "visit_motive_id_2": self._appt_data['visit_motive_id_2'],
                "agenda_id": str(self._appt_data['agenda_id']),
                "date": args["time"],
                "when": current_date
            }
        else:
            api_url = "https://hook.eu1.make.com/eyglocxmnyqa9f5eyr4t4vah9hx6h92g"
            request_body = {
                "config": self._config,
                "visit_motive_id": self._appt_data['visit_motive_id'],
                "agenda_id": str(self._appt_data['agenda_id']),
                "date": args["time"],
                "when": current_date
            }

        async def make_api_call():
            
            headers = {
                "Authorization": f"Bearer {self._api_token}",
                "Content-Type": "application/json"
            }
            response = await asyncio.to_thread(requests.post, api_url, json=request_body, headers=headers)
            return response.json()

        # Run API call and TTS concurrently
        api_result, _ = await asyncio.gather(
            make_api_call(),
            self._tts.say("Veuillez patienter s'il-vous-plaît, je vérifie nos disponibilités.")
        )

        self._api_call_in_progress = False

        data = api_result
        
        start_date = data.get("start_date")
        end_date = data.get("end_date")
        equipment_agenda_id = data.get("equipment_agenda_id")
        agenda_id = data.get("agenda_id")
        practitioner_agenda_id = data.get("practitioner_agenda_id")
        steps = data.get("steps")
        medecin = data.get("medecin")

        if self._new_patient:
            self._context.set_tools([{
                "type": "function",
                "function": {
                    "name": "save_new_patient_info",
                    "description": "Utilise cette fonction pour obtenir l'identité du patient",
                }
            }, {
                "type": "function",
                "function": {
                    "name": "ask_date",
                    "description": "Utilise cette fonction si le patiente ne souhaite pas le rendez-vous",
                },
            }])
        else:
            self._context.set_tools([{
                "type": "function",
                "function": {
                    "name": "confirm_appt",
                    "description": "Utilise cette fonction pour confirmer le rendez-vous si le patient a répondu",
                },
            }, {
                "type": "function",
                "function": {
                    "name": "ask_date",
                    "description": "Utilise cette fonction si le patient ne souhaite pas le rendez-vous",
                },
            }])

        if start_date != "error" and start_date is not None:
            self._appt_data["start_date"] = start_date
            self._appt_data["end_date"] = end_date
            self._appt_data["equipment_agenda_id"] = equipment_agenda_id
            self._appt_data["practitioner_agenda_id"] = practitioner_agenda_id
            self._appt_data["agenda_id"] = agenda_id
            self._appt_data["steps"] = steps
            self._appt_data["medecin"] = medecin
            lisible_date = convert_utc_to_french_text(start_date)
            # Compare requested date with available date
            requested_date = datetime.fromisoformat(args["time"].replace('Z', '+00:00'))
            available_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            
            date_message = f"Le rendez-vous le plus proche est le {lisible_date}"
            if medecin and medecin != "Unknown":
                date_message += f" avec le Docteur {medecin}"
            date_message += "."
            
            # Get tomorrow's date
            tomorrow = datetime.now().date() + timedelta(days=1)
            
            # Only show the sorry message if the requested date is not tomorrow
            if requested_date.date() != available_date.date() and requested_date.date() != tomorrow:
                date_message = f"Je suis désolé, il n'y a pas de disponibilité à la date que vous m'avez demandée. {date_message}"
            
            if self._new_patient:
                self._context.add_message({"role": "system", "content": f"{date_message} Cela vous convient-il ? Le rendez-vous n'est pas encore confirmé à cette étape. Si le patient dit oui, utilisez la fonction save_new_patient_info. Si le patient dit non et demande un autre horaire le même jour, utilisez la fonction check_availability avec une heure 2 heures plus tard le même jour. Si le patient dit non et souhaite une autre date, ou souhaite plutôt l'après-midi ou le matin, utilisez la fonction ask_date. Garde le format de date en toute lettre."})
            else:
                self._context.add_message({"role": "system", "content": f"{date_message} Cela vous convient-il ? Le rendez-vous n'est pas encore confirmé à cette étape. Si le patient dit oui, utilisez la fonction confirm_appt. Si le patient dit non et demande un autre horaire le même jour, utilisez la fonction check_availability avec une heure 2 heures plus tard le même jour. Si le patient dit non et souhaite une autre date, ou souhaite plutôt l'après-midi ou le matin, utilisez la fonction ask_date. Garde le format de date en toute lettre."})
        else:
            self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "set_question",
                "description": "Utilise cette fonction si le patient a d'autres questions",
            },
        }])

            self._context.add_message({"role": "system", "content": "Je suis désolé mais il n'y a pas de rendez-vous disponible actuellement. Je vous invite à réessayer plus tard, de nouveaux créneaux sont ouverts chaque jour."})
        
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
            
    async def set_question(self, function_name, tool_call_id, args, llm, context, result_callback):

        timing_config.TRANSCRIPTION_BUFFER_DELAY = 1.5

        self._new_patient = args.get("new_patient", False)
        
        add_string = ""

        if self._new_patient:
            self._patient_data = {}

            add_string = "Demande tout d'abord de dire et épeler le prénom, puis de dire et épeler le nom de famille, puis enfin la date de naissance du patient. En trois étapes."

            self._context.set_tools([
            {
                "type": "function",
                "function": {
                    "name": "recap_question",
                    "description": "utilise cette fonction si tu n'as pas résolu le problème du patient",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "problem": {
                                "type": "string",
                                "description": "Le problème détaillé du patient",
                            },
                            "fullname": {
                                "type": "string",
                                "description": "Le nom et le prénom du patient, doit correspondre à un prénom et un nom de famille crédibles français"
                            },
                            "birthdate": {
                                "type": "string",
                                "description": "La date de naissance du patient au format DD/MM/YYYY"
                            }
                        },
                        "required": ["problem"]
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "set_appt",
                    "description": "utilise cette fonction lorsque le patient souhaite prendre un rendez-vous, annuler ou modifier un rendez-vous"
                }
            }
        ])

        else:
            self._context.set_tools([
            {
                "type": "function",
                "function": {
                    "name": "recap_question",
                    "description": "utilise cette fonction si tu n'as pas résolu le problème du patient",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "problem": {
                                "type": "string",
                                "description": "Le problème détaillé du patient",
                            }
                        },
                        "required": ["problem"]
                    },
                },
            },
            {
                "type": "function",
                "function": {
                    "name": "set_appt",
                    "description": "utilise cette fonction lorsque le patient souhaite prendre un rendez-vous, annuler ou modifier un rendez-vous"
                }
            }
        ])

        self._context.add_message({
            "role": "system",
            "content": add_string + """
            
            Si tu peux répondre à la question, essaie d'aider le patient. Si tu n'as pas les informations nécessaires, propose de laisser une note au secrétariat. Pose des questions pour permettre au patient d'expliquer son problème.

            Uniquemeent si le patient parle de résultats ou de compte-rendus tu peux expliquer :
            Si le patient parle de résultats ou de compte-rendus seront disponibles 24 heures à 48 heures après votre rendez-vous. 
            Votre compte rendu et vos images sont disponible en ligne, vos identifiants sont inscrits sur l'étiquette remise au moment de votre sortie La remise des résultats est également possible sur place"

            Si le problème semble urgent, demande au patient de contacter l'hôpital le plus proche. 

            Si tu n'as pas résolu le problème puis utiliser le fonction recap_question. 

            Tu ne peux pas consulter les disponibilités ni prendre un rendez-vous à cette étape. 
            
            Si le patient souhaite finalement prendre, modifer, annuler un rendez-vous, utilise la fonction set_appt."""
        })
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def recap_question(self, function_name, tool_call_id, args, llm, context, result_callback):

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "set_question",
                "description": "Utilise cette fonction si le patient a d'autres questions",
            },
        }])


        if "fullname" in args:
            self._patient_data["fullname"] = args["fullname"]

        if "birthdate" in args:
            self._patient_data["birthdate"] = args["birthdate"]

        self._context.add_message({
            "role": "system", 
            "content": f"Explique que tu laisses une note au secrétariat. Demande si le patient a d'autres précisions."
        })
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def save_new_patient_info(self,
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback):
        
        timing_config.TRANSCRIPTION_BUFFER_DELAY = 3

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "get_last_name",
                "description": "Utilise cette fonction une fois que tu as le prénom du patient",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "first_name": {
                            "type": "string",
                            "description": "Le prénom du patient",
                        }
                    },
                    "required": ["first_name"]
                },
            },
        }])
        self._context.add_message({
            "role": "system", 
            "content": "Demandez au patient de dire et épeler son prénom. Une fois que vous avez le prénom, utilisez la fonction get_last_name."
        })
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def get_last_name(self,
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback):
        self._temp_patient_data = {"first_name": args["first_name"]}

        timing_config.TRANSCRIPTION_BUFFER_DELAY = 3
        
        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "get_birth_date",
                "description": "Utilise cette fonction une fois que tu as le nom de famille du patient",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "last_name": {
                            "type": "string",
                            "description": "Le nom de famille du patient",
                        }
                    },
                    "required": ["last_name"]
                },
            },
        }])
        self._context.add_message({
            "role": "system", 
            "content": "Maintenant, demandez au patient de dire et épeler son nom de famille sans prononcer son prénom. Une fois que vous avez le nom de famille, utilisez la fonction get_birth_date."
        })
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def get_birth_date(self,
            function_name,
            tool_call_id,
            args,
            llm,
            context,
            result_callback):
        self._temp_patient_data["last_name"] = args["last_name"]

        timing_config.TRANSCRIPTION_BUFFER_DELAY = 3
        
        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "confirm_new_patient_appt",
                "description": "Utilise cette fonction une fois que tu as la date de naissance du patient",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "first_name": {
                            "type": "string",
                            "description": "Le prénom du patient",
                        },
                        "last_name": {
                            "type": "string",
                            "description": "Le nom de famille du patient",
                        },
                        "birth_date": {
                            "type": "string",
                            "description": "La date de naissance du patient au format DD/MM/YYYY",
                        }
                    },
                    "required": ["first_name", "last_name", "birth_date"]
                },
            },
        }])
        self._context.add_message({
            "role": "system", 
            "content": "Dis : 'Quelle est votre date de naissance ?' Si tu ne comprends pas bien la date de naissance, explique que tu veux la date de naissance sous le format 20 Mars 1982 par exemple. Une fois que vous avez une date de naissance valide au bon format, utilisez la fonction confirm_new_patient_appt avec toutes les informations collectées."
        })
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def confirm_appt(self, function_name, tool_call_id, args, llm, context, result_callback):
        if self._appointment_confirmed:
            return

        self._appointment_confirmed = True

        # Check patient age if birth_date is available
        if 'birthdate' in self._patient_data:
            try:
                # Parse birth date (handle both DD/MM/YYYY and YYYY-MM-DD formats)
                birth_date_str = self._patient_data["birthdate"]
                try:
                    # First try DD/MM/YYYY format
                    birth_date = datetime.strptime(birth_date_str, "%d/%m/%Y")
                except ValueError:
                    # If that fails, try YYYY-MM-DD format
                    birth_date = datetime.strptime(birth_date_str, "%Y-%m-%d")
                
                today = datetime.now()
                age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
                
                # Check if visit motive contains echo/echographie
                visit_motive_name = self._appt_data.get('visit_motive_name', '').lower()
                print(visit_motive_name)
                
                # If patient is under 16 and exam is echography, create a task instead of an appointment
                if age < 16:
                    self._context.set_tools([{
                        "type": "function",
                        "function": {
                            "name": "set_question",
                            "description": "Utilise cette fonction si le patient a d'autres questions",
                        },
                    }])

                    self._context.add_message({
                        "role": "system", 
                        "content": f"J'ai bien noté que ce rendez-vous concerne un enfant de moins de 16 ans. Nous avons donc besoin d'organiser un créneau spécifique. J'ai transmis votre demande au secrétariat qui vous recontactera dans les meilleurs délais pour organiser ce rendez-vous. Avez-vous d'autres questions ?"
                    })
                    await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
                    return
            except Exception as e:
                logger.error(f"Error parsing birth date or calculating age: {str(e)}")
                # Continue with normal appointment flow if there's an error calculating age

        self._api_call_in_progress = True
        
        # Check if this is a double appointment
        is_double_appointment = 'visit_motive_id_1' in self._appt_data

        if is_double_appointment:
            self._appt_data["visit_motive_id"] = self._appt_data['visit_motive_id_1']
            self._appt_data["start_date"] = self._appt_data['steps'][0]['start_date']
            self._appt_data["end_date"] = self._appt_data['steps'][0]['end_date']
            
            api_url = "https://hook.eu1.make.com/ab3yaqta9aq811543q92u2dsa9l9k44n"
            request_body = {
                "config": self._config,
                "client": self._patient_data["first_name"] + " " + self._patient_data["last_name"],
                "visit_motive_id": self._appt_data['visit_motive_id'],
                "type": self._appt_data['visit_motive_name'],
                "callID": self._callId,
                "number": self._caller_phone_number,
                "patient_id": self._patient_data['id'],
                "appt_id": self._appt_data.get("appt_id", None),
                "start_date": self._appt_data['start_date'],
                "end_date": self._appt_data['end_date'],
                "steps": self._appt_data['steps'],
                "address": configContent['address'],
            }
        else:
            api_url = "https://hook.eu1.make.com/lbz9rr5yq6kr6evwo1uc7yanimwhhgg1"
            request_body = {
                "config": self._config,
                "client": self._patient_data["first_name"] + " " + self._patient_data["last_name"],
                "equipment_agenda_id": self._appt_data["equipment_agenda_id"],
                "practitioner_agenda_id": self._appt_data["practitioner_agenda_id"],
                "visit_motive_id": self._appt_data['visit_motive_id'],
                "type": self._appt_data['visit_motive_name'],
                "agenda_id": self._appt_data['agenda_id'],
                "start_date": self._appt_data['start_date'],
                "end_date": self._appt_data['end_date'],
                "patient_id": self._patient_data['id'],
                "appt_id": self._appt_data.get("appt_id", None),
                "callID": self._callId,
                "number": self._caller_phone_number,
                "address": configContent['address'],
            }

            if self._appt_data.get('steps'):
                request_body["steps"] = self._appt_data['steps']

        headers = {
            "Authorization": f"Bearer {self._api_token}",
            "Content-Type": "application/json"
        }
        
        # Run API call and TTS concurrently
        response, _ = await asyncio.gather(
            asyncio.to_thread(requests.post, api_url, json=request_body, headers=headers),
            self._tts.say("Je réserve votre rendez-vous.")
        )

        self._api_call_in_progress = False

        logger.info(f"Confirm appointment response: Status {response.status_code} - {response.text}")

        if response.status_code not in [200, 201, 202]:
            logger.error(f"Failed to confirm appointment: Status {response.status_code}")
            self._appointment_confirmed = False
            self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "set_question",
                "description": "Utilise cette fonction si le patient a d'autres questions",
            },
        }])

            self._context.add_message({
                "role": "system", 
                "content": "Je suis désolée, la réservation du rendez-vous a échoué. Je vous invite à réessayer plus tard ou à prendre rendez-vous sur notre site internet. Souhaitez-vous que je vous laisse une note pour que le secrétariat puisse vous recontacter ?"
            })
            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
            return

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "set_question",
                "description": "Utilise cette fonction si le patient a d'autres questions",
            },
        }])


        additional_warning = self._appt_data.get("additional_warning", "")
        start_date = self._appt_data.get("start_date", "")
        doctor_name = self._appt_data.get("medecin", "")
        doctor_message = f" avec le Docteur {doctor_name}" if doctor_name and doctor_name != "Unknown" else ""

        self._context.add_message({
            "role": "system", 
            "content": f"Le rendez-vous a été réservé avec succès pour le nouveau patient.  {additional_warning} Informez le patient que son rendez-vous est confirmé pour le {start_date}{doctor_message}. Expliquez qu'ils recevront un SMS de confirmation. Tu ne peux pas changer la date et l'heure du rendez-vous ni prendre d'autre rendez-vous. Demande au patient s'il a d'autres questions. Le patient doit rappeler s'il souhaite prendre un autre rendez-vous ou modifier celui-ci."
        })

        if sounds["ding2.wav"] is not None:
            await llm.push_frame(sounds["ding2.wav"], FrameDirection.DOWNSTREAM)
        else:
            logger.warning("Ding sound not available, skipping audio cue")
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def confirm_new_patient_appt(self, function_name, tool_call_id, args, llm, context, result_callback):
        if self._appointment_confirmed:
            return

        self._appointment_confirmed = True

        self._patient_data = {
            "first_name": args["first_name"],
            "last_name": args["last_name"],
            "birth_date": args["birth_date"]
        }

        # Check patient age
        try:
            # Parse birth date (handle both DD/MM/YYYY and YYYY-MM-DD formats)
            birth_date_str = args["birth_date"]
            try:
                # First try DD/MM/YYYY format
                birth_date = datetime.strptime(birth_date_str, "%d/%m/%Y")
            except ValueError:
                # If that fails, try YYYY-MM-DD format
                birth_date = datetime.strptime(birth_date_str, "%Y-%m-%d")
            
            today = datetime.now()
            age = today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))
            
            # Check if visit motive contains echo/echographie
            visit_motive_name = self._appt_data.get('visit_motive_name', '').lower()
            
            if age < 16:
                self._context.set_tools([{
                    "type": "function",
                    "function": {
                        "name": "set_question",
                        "description": "Utilise cette fonction si le patient a d'autres questions",
                    },
                }])

                self._context.add_message({
                    "role": "system", 
                    "content": f"J'ai bien noté que ce rendez-vous concerne un enfant de moins de 16 ans. Nous avons besoin d'organiser un créneau spécifique. J'ai transmis votre demande au secrétariat qui vous recontactera dans les meilleurs délais pour organiser ce rendez-vous. Avez-vous d'autres questions ?"
                })
                await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
                return
        except Exception as e:
            logger.error(f"Error parsing birth date or calculating age: {str(e)}")
            # Continue with normal appointment flow if there's an error calculating age

        self._api_call_in_progress = True
        
        # Check if this is a double appointment
        is_double_appointment = 'visit_motive_id_1' in self._appt_data

        if is_double_appointment:
            self._appt_data["visit_motive_id"] = self._appt_data['visit_motive_id_1']
            self._appt_data["start_date"] = self._appt_data['steps'][0]['start_date']
            self._appt_data["end_date"] = self._appt_data['steps'][0]['end_date']

            api_url = "https://hook.eu1.make.com/uh4xew3290yu4gahfdxe5adi7cx9ji2i"
            request_body = {
                "config": self._config,
                "first_name": args["first_name"],
                "last_name": args["last_name"],
                "birth_date": args["birth_date"],
                "visit_motive_id": self._appt_data['visit_motive_id'],
                "type": self._appt_data['visit_motive_name'],
                "callID": self._callId,
                "number": self._caller_phone_number,
                "steps": self._appt_data['steps'],
                "start_date": self._appt_data['start_date'],
                "end_date": self._appt_data['end_date'],
                "address": configContent['address'],
            }   
        
        else:
            api_url = "https://hook.eu1.make.com/df3daeb34m82au3wt0c1ib3pco5yghld"
            request_body = {
                "config": self._config,
                "first_name": args["first_name"],
                "last_name": args["last_name"],
                "birth_date": args["birth_date"],
                "visit_motive_id": self._appt_data['visit_motive_id'],
                "type": self._appt_data['visit_motive_name'],
                "practitioner_agenda_id": self._appt_data["practitioner_agenda_id"],
                "equipment_agenda_id": self._appt_data["equipment_agenda_id"],
                "agenda_id": self._appt_data['agenda_id'],
                "start_date": self._appt_data['start_date'],
                "end_date": self._appt_data['end_date'],
                "callID": self._callId,
                "number": self._caller_phone_number,
                "address": configContent['address'],
            }

        headers = {
            "Authorization": f"Bearer {self._api_token}",
            "Content-Type": "application/json"
        }
        
        # Run API call and TTS concurrently
        response, _ = await asyncio.gather(
            asyncio.to_thread(requests.post, api_url, json=request_body, headers=headers),
            self._tts.say("Je réserve votre rendez-vous.")
        )

        self._api_call_in_progress = False

        response_data = response.json()
        if "id" in response_data:
            self._appt_data["appointment_set_id"] = response_data["id"]

        logger.info(f"Confirm new patient appointment response: Status {response.status_code} - {response.text}")

        if response.status_code not in [200, 201, 202]:
            logger.error(f"Failed to confirm appointment: Status {response.status_code}")
            self._appointment_confirmed = False
            self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "set_question",
                "description": "Utilise cette fonction si le patient a d'autres questions",
            },
        }])

            self._context.add_message({
                "role": "system", 
                "content": "Je suis désolée, la réservation du rendez-vous a échoué. Je vous invite à réessayer plus tard ou à prendre rendez-vous sur notre site internet. Souhaitez-vous que je vous laisse une note pour que le secrétariat puisse puisse puisse vous recontacter ?"
            })
            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
            return

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "set_question",
                "description": "Utilise cette fonction si le patient a d'autres questions",
            },
        }])


        additional_warning = self._appt_data.get("additional_warning", "")
        start_date = self._appt_data.get("start_date", "")
        doctor_name = self._appt_data.get("medecin", "")
        doctor_message = f" avec le Docteur {doctor_name}" if doctor_name and doctor_name != "Unknown" else ""

        self._context.add_message({
            "role": "system", 
            "content": f"Le rendez-vous a été réservé avec succès pour le nouveau patient. {additional_warning} Informez le patient que son rendez-vous est confirmé pour le {start_date}{doctor_message}. Expliquez qu'ils recevront un SMS de confirmation. Tu ne peux pas changer la date et l'heure du rendez-vous ni prendre d'autre rendez-vous. Demande au patient s'il a d'autres questions. Le patient doit rappeler s'il souhaite prendre un autre rendez-vous ou modifier celui-ci."
        })

        if sounds["ding2.wav"] is not None:
            await llm.push_frame(sounds["ding2.wav"], FrameDirection.DOWNSTREAM)
        else:
            logger.warning("Ding sound not available, skipping audio cue")
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def cancel_appt(self, function_name, tool_call_id, args, llm, context, result_callback):

        self._api_call_in_progress = True

        api_url = "https://doctolib-secret-scrape-global.onrender.com/api/v1/remove-appt-with-following"
        request_body = {
            "config": self._config,
            "id": self._appt_data['appt_id']
        }
        headers = {
            "Authorization": f"Bearer {self._api_token}",
            "Content-Type": "application/json"
        }
        response = await asyncio.to_thread(requests.post, api_url, json=request_body, headers=headers)

        self._api_call_in_progress = False

        self._context.set_tools([{
            "type": "function",
            "function": {
                "name": "ask_date",
                "description": "Utilise cette fonction si le patient a besoin d'un autre rendez-vous"
            },
        }])

        self._context.add_message({"role": "system", "content": f"Le rendez-vous a été annulé avec succès. Informez le patient que son rendez-vous a été annulé. Demandez-lui s'il a besoin d'un autre rendez-vous. Si oui utilise la fonction ask_date. Sinon dites au revoir poliment."})

        if sounds["ding2.wav"] is not None:
            await llm.push_frame(sounds["ding2.wav"], FrameDirection.DOWNSTREAM)
        else:
            logger.warning("Ding sound not available, skipping audio cue")
        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

    
    async def appt_confirm(self, function_name, tool_call_id, args, llm, context, result_callback, intent):

        if 'appt_id' not in self._appt_data or self._appt_data.get('appt_id') is None:
            if not self._patient_data.get("appt_id"):
                await self._tts.say("Je n'ai pas trouvé le rendez-vous à " + intent)
                await self.set_question(function_name, tool_call_id, {"new_patient": False}, llm, context, result_callback)
                return

        # If patient has multiple appointments and wants to modify, take a note instead
        if self._patient_data.get("future_appointments_count", 0) > 1 and intent == "modifier":

            self._context.set_tools([{
                "type": "function",
                "function": {
                    "name": "set_question",
                    "description": "Utilise cette fonction si le patient a d'autres questions",
                },
            }])

            self._context.add_message({"role": "system", "content": "Dis : 'Je vois que vous avez plusieurs rendez-vous à venir. Pour éviter toute confusion, je vais laisser une note au secrétariat pour qu'ils puissent vous recontacter et réorganiser vos rendez-vous selon vos souhaits.' Demande ensuite si le patient a d'autres questions."})
            await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)
            return

        self._appt_data["appt_id"] = self._patient_data["appt_id"]
        self._appt_data["visit_motive_id"] = self._patient_data["visit_motive_id"]
        self._appt_data["visit_motive_name"] = self._patient_data["visit_motive_name"]
        self._appt_data["agenda_id"] = self._patient_data["agenda_id"]
        self._appt_data["instructions"] = ""
        self._appt_data["price"] = ""
        self._appt_data["after"] = ""
        self._appt_data["before"] = ""

        if intent == "modifier":
        
            self._context.set_tools([{
                "type": "function",
                "function": {
                    "name": "ask_date",
                    "description": "Utilise cette fonction si c'est le bon rendez-vous à modifier",
                },
            }, {
                "type": "function",
                "function": {
                    "name": "set_question",
                    "description": "utilise cette fonction si ce n'est pas le bon rendez-vous"
                },
            }])
            self._context.add_message({"role": "system", "content": f"Vérifie avec le patient si le rendez-vous à modifier est bien cette consultation {self._patient_data['visit_motive_name']} à cette date : {self._patient_data['appt_date']}. Si oui utilise la fonction ask_date. Sinon utilise la fonction set_question."})
                

        elif intent == "annuler":
            self._context.set_tools([{
                "type": "function",
                "function": {
                    "name": "cancel_appt",
                    "description": "Utilise cette fonction si c'est le bon rendez-vous à annuler",
                },
            }, {
                    "type": "function",
                    "function": {
                        "name": "set_question",
                        "description": "utilise cette fonction si ce n'est pas le bon rendez-vous"
                    },
                }])
            self._context.add_message({"role": "system", "content": f"Vérifie avec le patient si le rendez-vous à annuler est bien cette consultation {self._patient_data['visit_motive_name']} à cette date : {self._patient_data['appt_date']}. Si oui utilise la fonction cancel_appt. Sinon utilise la fonction set_question."})

        await llm.process_frame(OpenAILLMContextFrame(self._context), FrameDirection.DOWNSTREAM)

from base_main import base_main

async def main(room_url: str, token: str, callId: str, sipUri: str, config_id: str, caller_phone: str):

    # Récupération des examens pour le prompt global
    center_exams = ""
    try:
        if "inbound_config_file" in configContent and configContent["inbound_config_file"]:
            # Vérifier si inbound_config_file est déjà un dictionnaire ou une chaîne JSON
            if isinstance(configContent["inbound_config_file"], dict):
                config_json = configContent["inbound_config_file"]
            else:
                config_json = json.loads(configContent["inbound_config_file"])
            
            motives = config_json.get("visit-motives-categories", [])
            
            # Formater les motifs d'examens en une liste lisible
            # Inclure tous les motifs, qu'ils soient disponibles ou non
            available_motives = motives
            
            # Trier par nom pour une meilleure lisibilité
            available_motives.sort(key=lambda x: x.get("name", ""))
            
            # Créer la liste formatée
            formatted_exams = []
            for motive in available_motives:
                name = motive.get("name", "")
                if name:
                    formatted_exams.append(f"- {name}")
            
            center_exams = "\n".join(formatted_exams)
    except Exception as e:
        print(f"DEBUG - Erreur lors de la récupération des examens pour le prompt global: {str(e)}")

    global_prompt = f"""Nous sommes le {{current_date}}. Vous vous comporterez comme Marie, assistante agréable et professionnelle dans le centre d'imagerie Révélisse. Assurez-vous de rester polie, professionnelle et d'offrir une expérience patient agréable tout au long de la conversation. Ne dis bonjour qu'une seule fois. Reste courte dans tes réponses. Tu connais déjà le numéro de téléphone du patient. Ne conseille jamais de contacter le centre ou le secrétariat. Si les réponses de l'utilisateur semblent confuses c'est parce qu'elles viennent d'une retranscription téléphonique n'hésite pas à interpréter les transcriptions. Ne donne pas de conseils médicaux. Vous parlez français. Adresse du centre : {configContent["address"]}. Email du centre : {convert_email_to_spoken_text(configContent['email'])}. Si le patient souhaite parler à un humain, explique que personne n'est disponible pour le moment mais que tu peux prendre en note les demandes du patient pour qu'il puisse être recontacté plus tard. Horaires du centre : {configContent['openings']}. {configContent['additional_information']}

    Le centre réalise les examens suivants :
    {center_exams}"""

    try:
        await base_main(room_url, token, callId, sipUri, RevelisIntakeProcessor, config_id, global_prompt, caller_phone, configContent["search_url"])
    except Exception as e:
        logger.error(f"Error in Deltour main: {str(e)}")
        raise

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Revelis Flow")
    parser.add_argument("-u", type=str, help="Room URL")
    parser.add_argument("-t", type=str, help="Token")
    parser.add_argument("-i", type=str, help="Call ID")
    parser.add_argument("-s", type=str, help="SIP URI")
    parser.add_argument("-c", type=str, help="Config ID")
    parser.add_argument("-p", type=str, help="Caller phone number")
    config = parser.parse_args()

    if supabase is None:
        raise ValueError("Supabase client is not initialized")
    
    response = supabase.table("knowledge-bases").select("*").eq("config", config.c).execute()
    if len(response.data) == 0:
        raise ValueError(f"No config found for id {config.c}")
    
    configContent = response.data[0]

    asyncio.run(main(config.u, config.t, config.i, config.s, config.c, config.p))